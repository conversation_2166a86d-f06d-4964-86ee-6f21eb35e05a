import { z } from "zod";

// Base application status enum
export const ApplicationStatusEnum = z.enum([
  "pending",
  "under_review", 
  "approved",
  "rejected",
  "withdrawn",
]);

export type ApplicationStatus = z.infer<typeof ApplicationStatusEnum>;

// Document status enum
export const DocumentStatusEnum = z.enum([
  "pending",
  "uploaded",
  "verified", 
  "rejected",
]);

export type DocumentStatus = z.infer<typeof DocumentStatusEnum>;

// Listing type enum for applications
export const ListingTypeEnum = z.enum([
  "rental",
  "fractional",
  "ehailing-platform",
]);

export type ListingType = z.infer<typeof ListingTypeEnum>;

// E-hailing experience schema
export const EhailingExperienceSchema = z.object({
  hasEhailingExperience: z.boolean(),
  ehailingCompany: z.string().optional(),
  ehailingProfileNumber: z.string().optional(),
  ehailingWorkType: z.enum(["full-time", "part-time", "weekend"]).optional(),
  drivingExperienceYears: z.number().min(0).max(50).optional(),
  arrangementRequested: z.boolean().optional(),
});

export type EhailingExperience = z.infer<typeof EhailingExperienceSchema>;

// Applicant preferences for rental/fractional
export const ApplicantPreferencesSchema = z.object({
  minAge: z.number().min(18).max(100).optional(),
  drivingExperienceYears: z.number().min(0).max(50).optional(),
  gender: z.enum(["male", "female", "any"]).optional(),
});

export type ApplicantPreferences = z.infer<typeof ApplicantPreferencesSchema>;

// Rental/Fractional application details
export const RentalFractionalDetailsSchema = z.object({
  purpose: z.string().optional(),
  applicantPreferences: ApplicantPreferencesSchema.optional(),
});

export type RentalFractionalDetails = z.infer<typeof RentalFractionalDetailsSchema>;

// Combined application details schema
export const ApplicationDetailsSchema = z.union([
  // E-hailing application
  EhailingExperienceSchema,
  // Rental/Fractional application
  RentalFractionalDetailsSchema,
  // Generic object for flexibility
  z.record(z.any()),
]);

export type ApplicationDetails = z.infer<typeof ApplicationDetailsSchema>;

// Document upload schema
export const DocumentUploadSchema = z.object({
  documentType: z.string().min(1, "Document type is required"),
  documentUrl: z.string().url("Valid document URL is required"),
});

export type DocumentUpload = z.infer<typeof DocumentUploadSchema>;

// Application creation schema
export const CreateApplicationSchema = z.object({
  listingId: z.number().positive("Valid listing ID is required"),
  applicationDetails: ApplicationDetailsSchema,
});

export type CreateApplicationData = z.infer<typeof CreateApplicationSchema>;

// Document with status
export const ApplicationDocumentSchema = z.object({
  id: z.number(),
  documentType: z.string(),
  documentUrl: z.string(),
  uploadedAt: z.string(),
  status: DocumentStatusEnum.optional(),
});

export type ApplicationDocument = z.infer<typeof ApplicationDocumentSchema>;

// Applicant information
export const ApplicantSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
});

export type Applicant = z.infer<typeof ApplicantSchema>;

// Listing information for applications
export const ApplicationListingSchema = z.object({
  id: z.number(),
  listingType: ListingTypeEnum,
  listingDetails: z.record(z.any()),
});

export type ApplicationListing = z.infer<typeof ApplicationListingSchema>;

// Application decision
export const ApplicationDecisionSchema = z.object({
  decision: ApplicationStatusEnum,
  reason: z.string().optional(),
  decisionAt: z.string(),
  reviewerName: z.string().optional(),
});

export type ApplicationDecision = z.infer<typeof ApplicationDecisionSchema>;

// Full application with all related data
export const ApplicationWithDetailsSchema = z.object({
  id: z.number(),
  applicantId: z.number(),
  listingId: z.number(),
  applicationDetails: ApplicationDetailsSchema,
  createdAt: z.string(),
  
  // Related data
  applicant: ApplicantSchema,
  listing: ApplicationListingSchema,
  documents: z.array(ApplicationDocumentSchema),
  latestDecision: ApplicationDecisionSchema.optional(),
});

export type ApplicationWithDetails = z.infer<typeof ApplicationWithDetailsSchema>;

// Application form data for different types
export const EhailingApplicationFormSchema = z.object({
  listingId: z.number().positive(),
  hasEhailingExperience: z.boolean(),
  ehailingCompany: z.string().optional(),
  ehailingProfileNumber: z.string().optional(),
  ehailingWorkType: z.enum(["full-time", "part-time", "weekend"]).optional(),
  drivingExperienceYears: z.number().min(0).max(50).optional(),
  arrangementRequested: z.boolean().optional(),
  documents: z.array(DocumentUploadSchema).optional(),
});

export type EhailingApplicationForm = z.infer<typeof EhailingApplicationFormSchema>;

export const RentalApplicationFormSchema = z.object({
  listingId: z.number().positive(),
  purpose: z.string().min(1, "Purpose is required"),
  documents: z.array(DocumentUploadSchema).optional(),
});

export type RentalApplicationForm = z.infer<typeof RentalApplicationFormSchema>;

export const FractionalApplicationFormSchema = z.object({
  listingId: z.number().positive(),
  documents: z.array(DocumentUploadSchema).optional(),
});

export type FractionalApplicationForm = z.infer<typeof FractionalApplicationFormSchema>;

// Admin decision schema
export const MakeDecisionSchema = z.object({
  applicationId: z.number().positive(),
  decision: z.enum(["approved", "rejected"]),
  reason: z.string().optional(),
});

export type MakeDecisionData = z.infer<typeof MakeDecisionSchema>;

// Document status update schema
export const UpdateDocumentStatusSchema = z.object({
  documentId: z.number().positive(),
  status: z.enum(["verified", "rejected"]),
});

export type UpdateDocumentStatusData = z.infer<typeof UpdateDocumentStatusSchema>;

// Application statistics
export const ApplicationStatsSchema = z.object({
  total: z.number(),
  pending: z.number(),
  approved: z.number(),
  rejected: z.number(),
  byListingType: z.record(z.number()),
});

export type ApplicationStats = z.infer<typeof ApplicationStatsSchema>;

// API Response types
export const ApplicationResponseSchema = z.object({
  success: z.boolean(),
  applicationId: z.number().optional(),
  error: z.string().optional(),
});

export type ApplicationResponse = z.infer<typeof ApplicationResponseSchema>;

export const ApplicationsListResponseSchema = z.object({
  success: z.boolean(),
  applications: z.array(ApplicationWithDetailsSchema).optional(),
  error: z.string().optional(),
});

export type ApplicationsListResponse = z.infer<typeof ApplicationsListResponseSchema>;

export const ApplicationStatsResponseSchema = z.object({
  success: z.boolean(),
  stats: ApplicationStatsSchema.optional(),
  error: z.string().optional(),
});

export type ApplicationStatsResponse = z.infer<typeof ApplicationStatsResponseSchema>;

// Form state types for UI components
export interface ApplicationFormState {
  currentStep: "documents" | "initiation" | "experience";
  documents: {
    name: string;
    uploaded: boolean;
    required: boolean;
    file?: File;
    isSpecial?: boolean;
  }[];
  experience: EhailingExperience;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

// Document types for different application types
export const EHAILING_DOCUMENT_TYPES = [
  "ID Document",
  "Driver's license", 
  "Bank Statement - 3 months",
  "Proof of residence",
  "Selfie",
  "PrDP (Professional driving permit)",
  "Police clearance certificate",
] as const;

export const RENTAL_DOCUMENT_TYPES = [
  "ID Document",
  "Driver's license",
  "Bank Statement - 3 months", 
  "Proof of residence",
  "Selfie",
] as const;

export const FRACTIONAL_DOCUMENT_TYPES = [
  "ID Document",
  "Driver's license",
  "Bank Statement - 3 months",
  "Proof of residence", 
  "Selfie",
  "Proof of income",
] as const;

export type EhailingDocumentType = typeof EHAILING_DOCUMENT_TYPES[number];
export type RentalDocumentType = typeof RENTAL_DOCUMENT_TYPES[number];
export type FractionalDocumentType = typeof FRACTIONAL_DOCUMENT_TYPES[number];
