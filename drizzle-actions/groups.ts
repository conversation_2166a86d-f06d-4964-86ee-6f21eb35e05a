"use server";
import { db } from "@/db";
import { groupMemberRoles, groupMemberships, groups, party, groupMembershipInvitations, groupSharedVehicles, vehicles, partyStatus } from "@/drizzle/schema";
import { GroupCreationProps, GroupRoleEnum, GroupMembershipInvitationCreate } from "@/types/groups";
import { GroupCreate } from "@/types/groups";
import { CompanyPurposeEnum } from "@/types/company";
import { eq } from "drizzle-orm";
import { createGroupInvitationTask } from "@/actions/tasks";
import { createInitialVehiclePossession } from "./bookings";

import { context } from "esbuild";
import { PartyStatusEnum } from "@/types/party";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";

const groupPartyTypeId = 3;


export async function createGroup(groupData: GroupCreationProps) {

  const userAttributes = await getCurrentUserNoCache();

  console.log("userAttributes", userAttributes);
  const partyId = +(userAttributes?.["custom:db_id"] || 0);

  if (!partyId) {
    throw new Error("Party ID not found");
  }
  const transaction = await db.transaction(async (tx) => {


    const partyResult = await tx.insert(party).values({
      partyTypeId: groupPartyTypeId,

    }).returning();

    console.log("partyResult", partyResult);
    console.log("userAttributes", userAttributes);

    const partyStatusResult = await tx.insert(partyStatus).values({
      partyId: partyResult[0].id,
      status: PartyStatusEnum.ACTIVE,
    }).returning();


    const groupResult = await tx.insert(groups).values({
      partyId: partyResult[0].id, // Group partyId is the partyId of the group
      name: groupData.groupCreate.name,
      description: groupData.groupCreate.description,
      cityId: groupData.groupCreate.cityId,
      countryId: groupData.groupCreate.countryId,
      initialPurpose: groupData.groupCreate.InitialPurpose as CompanyPurposeEnum,
      isManaged: groupData.groupCreate.isManaged,
      createdBy: groupData.groupCreate.createdBy,
      creator: groupData.groupCreate.createdBy
    }).returning();

    const groupMembershipResult = await tx.insert(groupMemberships).values({
      partyId: partyId,
      groupId: groupResult[0].id,
      effectiveFrom: new Date().toISOString(),
      effectiveTo: null
    }).returning();

    const groupMemberRoleResult = await tx.insert(groupMemberRoles).values({
      groupId: groupResult[0].id,
      partyId: partyId, // Group member role partyId is the partyId of the member
      role: GroupRoleEnum.ADMIN,
      effectiveFrom: new Date().toISOString(),

    }).returning();

    // Handle member invitations if provided
    let invitationResults: any[] = [];
    if (groupData.memberInvitations && groupData.memberInvitations.length > 0) {
      const invitationsToInsert = groupData.memberInvitations.map(invitation => ({
        groupId: groupResult[0].id,
        firstName: invitation.firstName,
        lastName: invitation.lastName,
        email: invitation.email,
        role: invitation.role,
        invitedBy: partyId,
        invitationToken: crypto.randomUUID(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      }));

      invitationResults = await tx.insert(groupMembershipInvitations).values(invitationsToInsert).returning();
      
      // Create tasks for each invitation
      if (invitationResults.length > 0) {
        const inviterName = userAttributes?.given_name && userAttributes?.family_name 
          ? `${userAttributes.given_name} ${userAttributes.family_name}`.trim()
          : userAttributes?.email || 'Someone';
          
        for (const invitation of invitationResults) {
          try {
            await createGroupInvitationTask(
              invitation.email,
              invitation.id,
              groupResult[0].id,
              groupResult[0].name,
              inviterName,
              invitation.role,
              undefined // partyId not known yet for invitee
            );
            console.log(`✅ Created GROUP_INVITATION task for ${invitation.email}`);
          } catch (error) {
            console.error(`❌ Failed to create task for invitation ${invitation.id}:`, error);
            // Don't fail the whole transaction for task creation errors
          }
        }
      }
    }

  

    if (!groupData.vehicleCreate) {
      throw new Error("Vehicle is required");
    }

    const vehicleResult = await tx.insert(vehicles).values({
      partyId: partyId,
      modelId: groupData.vehicleCreate.model_id,
      vinNumber: groupData.vehicleCreate.vin_number,
      vehicleRegistration: groupData.vehicleCreate.vehicle_registration,
      countryId: groupData.vehicleCreate.country_id,
      isActive: true,
      manufacturingYear: groupData.vehicleCreate.manufacturing_year,
      color: groupData.vehicleCreate.color,

    }).returning();

    const result = await tx.insert(groupSharedVehicles).values({
      groupId: groupResult[0].id,
      vehicleId: vehicleResult[0].id,
      sharedBy: partyId,
      effectiveFrom: new Date().toISOString(),
      isActive: true,
    }).returning();

    // Create initial vehicle possession for the creator
    // Note: This is outside the transaction to avoid circular dependency issues
    // We'll create it after the transaction completes

    const finalResult = {
      party: partyResult[0],
      group: groupResult[0],
      groupMembership: groupMembershipResult[0],
      groupMemberRole: groupMemberRoleResult[0],
      invitations: invitationResults,
      partyStatus: partyStatusResult[0],
      vehicle: vehicleResult[0],
      groupSharedVehicle: result[0],
    }

    console.log("finalResult", finalResult);
    return finalResult;
  });


  // Create initial possession after the transaction to avoid circular dependency
  try {
    await createInitialVehiclePossession(
      transaction.vehicle.id,
      partyId, // The group creator becomes the initial possessor
      partyId, // The creator records this event
      'OWNER' as any, // Cast to handle enum compatibility
      'OWNERSHIP_TRANSFER',
      `Initial possession when vehicle was added to group "${transaction.group.name}"`
    );
    console.log(`✅ Created initial possession for vehicle ${transaction.vehicle.id} in group ${transaction.group.name}`);
  } catch (error) {
    console.error("❌ Failed to create initial vehicle possession:", error);
    // Don't fail the group creation if possession creation fails
  }

  return transaction;
}


