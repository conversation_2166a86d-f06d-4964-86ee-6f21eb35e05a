"use server";

import { db } from "../../db";
import {
  h_listings,
  h_listing_approval_status,
} from "@/drizzle/h_schema/listings";
import { party, contactPoint, contactPointType } from "@/drizzle/schema";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  individual,
  users,
} from "@/drizzle/schema";
import { h_vehicleCatalog } from "@/drizzle/h_schema/vehicle-catalog";
import { h_listing_publish_status } from "@/drizzle/h_schema/listings";
import { eq, desc, and, or, sql } from "drizzle-orm";
import { getUserAttributes } from "@/lib/serverUserAttributes";

export interface ListingApprovalData {
  id: number;
  partyId: number;
  sourceType: "catalog" | "vehicle";
  sourceId: number;
  listingType: "rental" | "fractional" | "ehailing-platform";
  effectiveFrom: string;
  effectiveTo: string | null;
  listingDetails: string;

  // Approval status info
  approvalStatus:
    | "pending"
    | "under_review"
    | "approved"
    | "rejected"
    | "withdrawn";
  approvalReason?: string | null;
  approvalStatusAt: string;
  approvalStatusBy?: number | null;

  // Party/User info
  partyName?: string;
  partyEmail?: string;
  contactEmail?: string;
  contactPhone?: string;

  // Source details (vehicle or catalog)
  sourceDetails: {
    make?: string;
    model?: string;
    year?: number;
    variant?: string;
    registrationNumber?: string;
    // For catalog items
    catalogName?: string;
    catalogDescription?: string;
  };
}

export async function getPendingListingApprovals(): Promise<
  ListingApprovalData[]
> {
  try {
    const results = await db
      .select({
        // Listing fields
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,

        // Approval status fields
        approvalStatus: h_listing_approval_status.status,
        approvalReason: h_listing_approval_status.reason,
        approvalStatusAt: h_listing_approval_status.statusAt,
        approvalStatusBy: h_listing_approval_status.statusBy,

        // Party info
        partyName: sql<string>`COALESCE(CONCAT(${individual.firstName}, ' ', ${individual.lastName}), ${users.username}, 'Unknown User')`,
        partyEmail: sql<string>`${users.email}`,

        // Vehicle details (when sourceType = 'vehicle')
        vehicleMake: vehicleMake.name,
        vehicleModel: vehicleModel.model,
        vehicleYear: vehicles.manufacturingYear,
        vehicleVariant: sql<string>`''`, // No variant in vehicles table
        vehicleRegistration: vehicles.vehicleRegistration,

        // Catalog details (when sourceType = 'catalog')
        catalogName: h_vehicleCatalog.make,
        catalogDescription: h_vehicleCatalog.description,
      })
      .from(h_listings)
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .leftJoin(party, eq(h_listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(users, eq(party.externalId, sql`${users.id}::text`))
      // Left join for vehicle details
      .leftJoin(
        vehicles,
        and(
          eq(h_listings.sourceType, "vehicle"),
          eq(h_listings.sourceId, vehicles.id)
        )
      )
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      // Left join for catalog details
      .leftJoin(
        h_vehicleCatalog,
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listings.sourceId, h_vehicleCatalog.id)
        )
      )
      .where(
        or(
          eq(h_listing_approval_status.status, "pending"),
          eq(h_listing_approval_status.status, "under_review")
        )
      )
      .orderBy(desc(h_listing_approval_status.statusAt));

    return results.map((row: any) => ({
      id: row.id,
      partyId: row.partyId,
      sourceType: row.sourceType as "catalog" | "vehicle",
      sourceId: row.sourceId,
      listingType: row.listingType as
        | "rental"
        | "fractional"
        | "ehailing-platform",
      effectiveFrom: row.effectiveFrom,
      effectiveTo: row.effectiveTo,
      listingDetails: row.listingDetails,

      approvalStatus: row.approvalStatus as
        | "pending"
        | "under_review"
        | "approved"
        | "rejected"
        | "withdrawn",
      approvalReason: row.approvalReason,
      approvalStatusAt: row.approvalStatusAt,
      approvalStatusBy: row.approvalStatusBy,

      partyName: row.partyName,
      partyEmail: row.partyEmail,

      sourceDetails: {
        // Vehicle details
        make: row.vehicleMake || undefined,
        model: row.vehicleModel || undefined,
        year: row.vehicleYear || undefined,
        variant: row.vehicleVariant || undefined,
        registrationNumber: row.vehicleRegistration || undefined,
        // Catalog details
        catalogName: row.catalogName || undefined,
        catalogDescription: row.catalogDescription || undefined,
      },
    }));
  } catch (error) {
    console.error("Error fetching pending listing approvals:", error);
    throw new Error("Failed to fetch pending listing approvals");
  }
}

export async function getAllListingApprovals(
  searchQuery?: string,
  statusFilter?: string,
  typeFilter?: string
): Promise<ListingApprovalData[]> {
  try {
    let whereConditions = [];

    // Status filter
    if (statusFilter && statusFilter !== "all") {
      whereConditions.push(
        eq(h_listing_approval_status.status, statusFilter as any)
      );
    }

    // Type filter
    if (typeFilter && typeFilter !== "all") {
      whereConditions.push(eq(h_listings.listingType, typeFilter as any));
    }

    const results = await db
      .select({
        // Listing fields
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,

        // Approval status fields
        approvalStatus: h_listing_approval_status.status,
        approvalReason: h_listing_approval_status.reason,
        approvalStatusAt: h_listing_approval_status.statusAt,
        approvalStatusBy: h_listing_approval_status.statusBy,

        // Party info
        partyName: sql<string>`COALESCE(CONCAT(${individual.firstName}, ' ', ${individual.lastName}), ${users.username}, 'Unknown User')`,
        partyEmail: sql<string>`${users.email}`,

        // Vehicle details
        vehicleMake: vehicleMake.name,
        vehicleModel: vehicleModel.model,
        vehicleYear: vehicles.manufacturingYear,
        vehicleVariant: sql<string>`''`, // No variant in vehicles table
        vehicleRegistration: vehicles.vehicleRegistration,

        // Catalog details
        catalogName: h_vehicleCatalog.make,
        catalogDescription: h_vehicleCatalog.description,
      })
      .from(h_listings)
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .leftJoin(party, eq(h_listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(users, eq(party.externalId, sql`${users.id}::text`))
      .leftJoin(
        vehicles,
        and(
          eq(h_listings.sourceType, "vehicle"),
          eq(h_listings.sourceId, vehicles.id)
        )
      )
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(
        h_vehicleCatalog,
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listings.sourceId, h_vehicleCatalog.id)
        )
      )
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(h_listing_approval_status.statusAt));

    // Apply search filter in memory (could be optimized with SQL LIKE queries)
    let filteredResults = results;
    if (searchQuery && searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filteredResults = results.filter((row: any) => {
        const searchableText = [
          row.partyName,
          row.partyEmail,
          row.vehicleMake,
          row.vehicleModel,
          row.vehicleVariant,
          row.vehicleRegistration,
          row.catalogName,
          row.catalogDescription,
        ]
          .join(" ")
          .toLowerCase();

        return searchableText.includes(query);
      });
    }

    return filteredResults.map((row: any) => ({
      id: row.id,
      partyId: row.partyId,
      sourceType: row.sourceType as "catalog" | "vehicle",
      sourceId: row.sourceId,
      listingType: row.listingType as
        | "rental"
        | "fractional"
        | "ehailing-platform",
      effectiveFrom: row.effectiveFrom,
      effectiveTo: row.effectiveTo,
      listingDetails: row.listingDetails,

      approvalStatus: row.approvalStatus as
        | "pending"
        | "under_review"
        | "approved"
        | "rejected"
        | "withdrawn",
      approvalReason: row.approvalReason,
      approvalStatusAt: row.approvalStatusAt,
      approvalStatusBy: row.approvalStatusBy,

      partyName: row.partyName,
      partyEmail: row.partyEmail,

      sourceDetails: {
        make: row.vehicleMake || undefined,
        model: row.vehicleModel || undefined,
        year: row.vehicleYear || undefined,
        variant: row.vehicleVariant || undefined,
        registrationNumber: row.vehicleRegistration || undefined,
        catalogName: row.catalogName || undefined,
        catalogDescription: row.catalogDescription || undefined,
      },
    }));
  } catch (error) {
    console.error("Error fetching listing approvals:", error);
    throw new Error("Failed to fetch listing approvals");
  }
}

/**
 * Get current admin user's party ID from authentication
 */
async function getCurrentAdminPartyId(): Promise<number | null> {
  try {
    const userAttributes = await getUserAttributes();
    if (!userAttributes) {
      console.error("No authenticated user found");
      return null;
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      console.error("No party ID found in user attributes");
      return null;
    }

    return parseInt(partyId);
  } catch (error) {
    console.error("Error getting current admin party ID:", error);
    return null;
  }
}

export async function approveListing(
  listingId: number,
  reason?: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get current admin user
    const adminPartyId = await getCurrentAdminPartyId();
    if (!adminPartyId) {
      return { success: false, message: "Authentication required" };
    }

    const now = new Date().toISOString();

    // Update approval status
    await db
      .update(h_listing_approval_status)
      .set({
        status: "approved",
        reason: reason || null,
        statusAt: now,
        statusBy: adminPartyId,
      })
      .where(eq(h_listing_approval_status.listingId, listingId));

    // Insert publish status (approved listings should be published)
    await db.insert(h_listing_publish_status).values({
      listingId: listingId,
      status: "published",
      statusAt: now,
      statusBy: adminPartyId,
    });

    return {
      success: true,
      message: "Listing approved and published successfully",
    };
  } catch (error) {
    console.error("Error approving listing:", error);
    return { success: false, message: "Failed to approve listing" };
  }
}

export async function rejectListing(
  listingId: number,
  reason: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get current admin user
    const adminPartyId = await getCurrentAdminPartyId();
    if (!adminPartyId) {
      return { success: false, message: "Authentication required" };
    }

    const now = new Date().toISOString();

    // Update approval status
    await db
      .update(h_listing_approval_status)
      .set({
        status: "rejected",
        reason: reason,
        statusAt: now,
        statusBy: adminPartyId,
      })
      .where(eq(h_listing_approval_status.listingId, listingId));

    // Insert publish status (rejected listings should be archived)
    await db.insert(h_listing_publish_status).values({
      listingId: listingId,
      status: "archived",
      statusAt: now,
      statusBy: adminPartyId,
    });

    return { success: true, message: "Listing rejected successfully" };
  } catch (error) {
    console.error("Error rejecting listing:", error);
    return { success: false, message: "Failed to reject listing" };
  }
}

export async function bulkApproveListing(
  listingIds: number[],
  reason?: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get current admin user
    const adminPartyId = await getCurrentAdminPartyId();
    if (!adminPartyId) {
      return { success: false, message: "Authentication required" };
    }

    const now = new Date().toISOString();

    // Update approval status for all listings
    await db
      .update(h_listing_approval_status)
      .set({
        status: "approved",
        reason: reason || null,
        statusAt: now,
        statusBy: adminPartyId,
      })
      .where(sql`${h_listing_approval_status.listingId} = ANY(${listingIds})`);

    // Insert publish status for all approved listings
    const publishStatusValues = listingIds.map((listingId) => ({
      listingId,
      status: "published" as const,
      statusAt: now,
      statusBy: adminPartyId,
    }));

    await db.insert(h_listing_publish_status).values(publishStatusValues);

    return {
      success: true,
      message: `${listingIds.length} listings approved and published successfully`,
    };
  } catch (error) {
    console.error("Error bulk approving listings:", error);
    return { success: false, message: "Failed to approve listings" };
  }
}

export async function bulkRejectListing(
  listingIds: number[],
  reason: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get current admin user
    const adminPartyId = await getCurrentAdminPartyId();
    if (!adminPartyId) {
      return { success: false, message: "Authentication required" };
    }

    const now = new Date().toISOString();

    // Update approval status for all listings
    await db
      .update(h_listing_approval_status)
      .set({
        status: "rejected",
        reason: reason,
        statusAt: now,
        statusBy: adminPartyId,
      })
      .where(sql`${h_listing_approval_status.listingId} = ANY(${listingIds})`);

    // Insert publish status for all rejected listings (archived)
    const publishStatusValues = listingIds.map((listingId) => ({
      listingId,
      status: "archived" as const,
      statusAt: now,
      statusBy: adminPartyId,
    }));

    await db.insert(h_listing_publish_status).values(publishStatusValues);

    return {
      success: true,
      message: `${listingIds.length} listings rejected successfully`,
    };
  } catch (error) {
    console.error("Error bulk rejecting listings:", error);
    return { success: false, message: "Failed to reject listings" };
  }
}

export async function getListingApprovalById(
  listingId: number
): Promise<ListingApprovalData | null> {
  try {
    const result = await db
      .select({
        // Listing fields
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,

        // Approval status fields
        approvalStatus: h_listing_approval_status.status,
        approvalReason: h_listing_approval_status.reason,
        approvalStatusAt: h_listing_approval_status.statusAt,
        approvalStatusBy: h_listing_approval_status.statusBy,

        // Party info
        partyName: sql<string>`COALESCE(CONCAT(${individual.firstName}, ' ', ${individual.lastName}), ${users.username}, 'Unknown User')`,
        partyEmail: sql<string>`${users.email}`,

        // Contact information from contact_point table (case-insensitive, fallback to any if no primary)
        contactEmail: sql<string>`(
          SELECT COALESCE(
            (SELECT cp.value
             FROM contact_point cp
             JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id
             WHERE cp.party_id = ${h_listings.partyId}
               AND LOWER(cpt.name) = 'email'
               AND cp.is_primary = true
             LIMIT 1),
            (SELECT cp.value
             FROM contact_point cp
             JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id
             WHERE cp.party_id = ${h_listings.partyId}
               AND LOWER(cpt.name) = 'email'
             LIMIT 1)
          )
        )`,
        contactPhone: sql<string>`(
          SELECT COALESCE(
            (SELECT cp.value
             FROM contact_point cp
             JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id
             WHERE cp.party_id = ${h_listings.partyId}
               AND LOWER(cpt.name) = 'phone'
               AND cp.is_primary = true
             LIMIT 1),
            (SELECT cp.value
             FROM contact_point cp
             JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id
             WHERE cp.party_id = ${h_listings.partyId}
               AND LOWER(cpt.name) = 'phone'
             LIMIT 1)
          )
        )`,

        // Vehicle details (when sourceType = 'vehicle')
        vehicleMake: vehicleMake.name,
        vehicleModel: vehicleModel.model,
        vehicleYear: vehicles.manufacturingYear,
        vehicleVariant: sql<string>`''`, // No variant in vehicles table
        vehicleRegistration: vehicles.vehicleRegistration,

        // Catalog details (when sourceType = 'catalog')
        catalogName: h_vehicleCatalog.make,
        catalogDescription: h_vehicleCatalog.description,
      })
      .from(h_listings)
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .leftJoin(party, eq(h_listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(users, eq(party.externalId, sql`${users.id}::text`))
      // Left join for vehicle details
      .leftJoin(
        vehicles,
        and(
          eq(h_listings.sourceType, "vehicle"),
          eq(h_listings.sourceId, vehicles.id)
        )
      )
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      // Left join for catalog details
      .leftJoin(
        h_vehicleCatalog,
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listings.sourceId, h_vehicleCatalog.id)
        )
      )
      .where(eq(h_listings.id, listingId))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    const row = result[0];

    // Debug: Log the contact information
    console.log("🔍 Debug - Contact info for listing:", {
      id: listingId,
      partyId: row.partyId,
      partyName: row.partyName,
      partyEmail: row.partyEmail,
      contactEmail: row.contactEmail,
      contactPhone: row.contactPhone,
    });

    // Debug: Check what contact point types exist
    const contactTypes = await db
      .select({ id: contactPointType.id, name: contactPointType.name })
      .from(contactPointType)
      .where(eq(contactPointType.isActive, true));
    console.log("🔍 Debug - Available contact point types:", contactTypes);

    // Debug: Check what contact points exist for this party
    const partyContacts = await db
      .select({
        id: contactPoint.id,
        value: contactPoint.value,
        isPrimary: contactPoint.isPrimary,
        typeName: contactPointType.name,
      })
      .from(contactPoint)
      .leftJoin(
        contactPointType,
        eq(contactPoint.contactPointTypeId, contactPointType.id)
      )
      .where(eq(contactPoint.partyId, row.partyId));
    console.log("🔍 Debug - Contact points for party:", partyContacts);

    return {
      id: row.id,
      partyId: row.partyId,
      sourceType: row.sourceType as "catalog" | "vehicle",
      sourceId: row.sourceId,
      listingType: row.listingType as
        | "rental"
        | "fractional"
        | "ehailing-platform",
      effectiveFrom: row.effectiveFrom,
      effectiveTo: row.effectiveTo,
      listingDetails: row.listingDetails,

      approvalStatus: row.approvalStatus as
        | "pending"
        | "under_review"
        | "approved"
        | "rejected"
        | "withdrawn",
      approvalReason: row.approvalReason,
      approvalStatusAt: row.approvalStatusAt,
      approvalStatusBy: row.approvalStatusBy,

      partyName: row.partyName,
      partyEmail: row.partyEmail,
      contactEmail: row.contactEmail,
      contactPhone: row.contactPhone,

      sourceDetails: {
        make: row.vehicleMake || row.catalogName || undefined,
        model: row.vehicleModel || undefined,
        year: row.vehicleYear || undefined,
        variant: row.vehicleVariant || undefined,
        registrationNumber: row.vehicleRegistration || undefined,
        catalogName: row.catalogName || undefined,
        catalogDescription: row.catalogDescription || undefined,
      },
    };
  } catch (error) {
    console.error("Error fetching listing approval by ID:", error);
    return null;
  }
}
