"use server";

/**
 * DRIZZLE ACTIONS - APPLICATIONS
 *
 * This file contains all direct database operations for applications using Drizzle ORM.
 *
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Server actions for form handling that call these functions
 *
 * Schema Tables Used:
 * - h_applications: Main application records
 * - h_applicationDocuments: Document uploads for applications
 * - h_applicationDocumentsStatus: Document verification status
 * - h_applicationDecisions: Admin decisions on applications
 * - h_listings: Referenced listings
 * - party: Applicants and reviewers
 */

import { db } from "../db";
import { eq, desc, and, sql, inArray } from "drizzle-orm";
import {
  h_applications,
  h_applicationDocuments,
  h_applicationDocumentsStatus,
  h_applicationDecisions,
} from "@/drizzle/h_schema/applications";
import { h_listings } from "@/drizzle/h_schema/listings";
import { party, individual } from "@/drizzle/schema";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Types for application data
export interface ApplicationData {
  listingId: number;
  applicationDetails: {
    // E-hailing specific
    hasEhailingExperience?: boolean;
    ehailingCompany?: string;
    ehailingProfileNumber?: string;
    ehailingWorkType?: string;
    drivingExperienceYears?: string;
    arrangementRequested?: boolean;

    // Rental/Fractional specific
    purpose?: string;
    applicantPreferences?: {
      minAge?: number;
      drivingExperienceYears?: string;
      gender?: string;
    };
  };
}

export interface DocumentUpload {
  documentType: string;
  documentUrl: string;
}

export interface ApplicationWithDetails {
  id: number;
  applicantId: number;
  listingId: number;
  applicationDetails: any;
  createdAt: string;

  // Related data
  applicant: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  listing: {
    id: number;
    listingType: string;
    listingDetails: any;
  };
  documents: {
    id: number;
    documentType: string;
    documentUrl: string;
    uploadedAt: string;
    status?: string;
  }[];
  latestDecision?: {
    decision: string;
    reason?: string;
    decisionAt: string;
    reviewerName?: string;
  };
}

/**
 * Create a new application
 */
export async function createApplication(
  applicationData: ApplicationData
): Promise<{ success: boolean; applicationId?: number; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    // Verify the listing exists and is published
    const listing = await db
      .select()
      .from(h_listings)
      .where(eq(h_listings.id, applicationData.listingId))
      .limit(1);

    if (listing.length === 0) {
      return { success: false, error: "Listing not found" };
    }

    // Check if user already has an application for this listing
    const existingApplication = await db
      .select()
      .from(h_applications)
      .where(
        and(
          eq(h_applications.applicantId, parseInt(partyId)),
          eq(h_applications.listingId, applicationData.listingId)
        )
      )
      .limit(1);

    if (existingApplication.length > 0) {
      return {
        success: false,
        error: "You have already applied for this listing",
      };
    }

    // Create the application
    const result = await db
      .insert(h_applications)
      .values({
        applicantId: parseInt(partyId),
        listingId: applicationData.listingId,
        applicationDetails: JSON.stringify(applicationData.applicationDetails),
      })
      .returning({ id: h_applications.id });

    return { success: true, applicationId: result[0].id };
  } catch (error) {
    console.error("Error creating application:", error);
    return { success: false, error: "Failed to create application" };
  }
}

/**
 * Upload documents for an application
 */
export async function uploadApplicationDocuments(
  applicationId: number,
  documents: DocumentUpload[]
): Promise<{ success: boolean; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    // Verify the application belongs to the user
    const application = await db
      .select()
      .from(h_applications)
      .where(
        and(
          eq(h_applications.id, applicationId),
          eq(h_applications.applicantId, parseInt(partyId))
        )
      )
      .limit(1);

    if (application.length === 0) {
      return {
        success: false,
        error: "Application not found or access denied",
      };
    }

    // Insert documents
    const documentInserts = documents.map((doc) => ({
      applicationId,
      documentType: doc.documentType,
      documentUrl: doc.documentUrl,
    }));

    await db.insert(h_applicationDocuments).values(documentInserts);

    return { success: true };
  } catch (error) {
    console.error("Error uploading application documents:", error);
    return { success: false, error: "Failed to upload documents" };
  }
}

/**
 * Get user's applications
 */
export async function getUserApplications(): Promise<{
  success: boolean;
  applications?: ApplicationWithDetails[];
  error?: string;
}> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    const applications = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,
        createdAt: sql<string>`${h_applications.id}::text`, // Placeholder for created_at

        // Listing details
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,

        // Applicant details
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`''`, // Placeholder - need to get from contact points
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applications.applicantId, parseInt(partyId)))
      .orderBy(desc(h_applications.id));

    // Get documents for each application
    const applicationIds = applications.map((app) => app.id);
    const documents =
      applicationIds.length > 0
        ? await db
            .select()
            .from(h_applicationDocuments)
            .where(
              inArray(h_applicationDocuments.applicationId, applicationIds)
            )
        : [];

    // Get latest decisions for each application
    const decisions =
      applicationIds.length > 0
        ? await db
            .select({
              applicationId: h_applicationDecisions.applicationId,
              decision: h_applicationDecisions.decision,
              reason: h_applicationDecisions.reason,
              decisionAt: h_applicationDecisions.decisionAt,
            })
            .from(h_applicationDecisions)
            .where(
              inArray(h_applicationDecisions.applicationId, applicationIds)
            )
            .orderBy(desc(h_applicationDecisions.decisionAt))
        : [];

    // Combine data
    const result: ApplicationWithDetails[] = applications.map((app) => ({
      id: app.id,
      applicantId: app.applicantId,
      listingId: app.listingId,
      applicationDetails: app.applicationDetails
        ? JSON.parse(app.applicationDetails)
        : {},
      createdAt: new Date().toISOString(), // TODO: Add proper timestamp

      applicant: {
        id: app.applicantId,
        firstName: app.firstName || "",
        lastName: app.lastName || "",
        email: app.email || "",
      },
      listing: {
        id: app.listingId,
        listingType: app.listingType,
        listingDetails: app.listingDetails
          ? JSON.parse(app.listingDetails)
          : {},
      },
      documents: documents
        .filter((doc) => doc.applicationId === app.id)
        .map((doc) => ({
          id: doc.id,
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          uploadedAt: doc.uploadedAt.toISOString(),
        })),
      latestDecision: decisions.find((dec) => dec.applicationId === app.id)
        ? {
            decision: decisions.find((dec) => dec.applicationId === app.id)!
              .decision,
            reason:
              decisions.find((dec) => dec.applicationId === app.id)!.reason ||
              undefined,
            decisionAt: decisions
              .find((dec) => dec.applicationId === app.id)!
              .decisionAt.toISOString(),
          }
        : undefined,
    }));

    return { success: true, applications: result };
  } catch (error) {
    console.error("Error fetching user applications:", error);
    return { success: false, error: "Failed to fetch applications" };
  }
}

/**
 * Get a specific application with full details
 */
export async function getApplicationById(applicationId: number): Promise<{
  success: boolean;
  application?: ApplicationWithDetails;
  error?: string;
}> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    const application = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,

        // Listing details
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,

        // Applicant details
        firstName: individual.firstName,
        lastName: individual.lastName,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(
        and(
          eq(h_applications.id, applicationId),
          eq(h_applications.applicantId, parseInt(partyId))
        )
      )
      .limit(1);

    if (application.length === 0) {
      return {
        success: false,
        error: "Application not found or access denied",
      };
    }

    const app = application[0];

    // Get documents
    const documents = await db
      .select()
      .from(h_applicationDocuments)
      .where(eq(h_applicationDocuments.applicationId, applicationId));

    // Get latest decision
    const decisions = await db
      .select({
        decision: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        decisionAt: h_applicationDecisions.decisionAt,
      })
      .from(h_applicationDecisions)
      .where(eq(h_applicationDecisions.applicationId, applicationId))
      .orderBy(desc(h_applicationDecisions.decisionAt))
      .limit(1);

    const result: ApplicationWithDetails = {
      id: app.id,
      applicantId: app.applicantId,
      listingId: app.listingId,
      applicationDetails: app.applicationDetails
        ? JSON.parse(app.applicationDetails)
        : {},
      createdAt: new Date().toISOString(), // TODO: Add proper timestamp

      applicant: {
        id: app.applicantId,
        firstName: app.firstName || "",
        lastName: app.lastName || "",
        email: "", // TODO: Get from contact points
      },
      listing: {
        id: app.listingId,
        listingType: app.listingType,
        listingDetails: app.listingDetails
          ? JSON.parse(app.listingDetails)
          : {},
      },
      documents: documents.map((doc) => ({
        id: doc.id,
        documentType: doc.documentType,
        documentUrl: doc.documentUrl,
        uploadedAt: doc.uploadedAt.toISOString(),
      })),
      latestDecision:
        decisions.length > 0
          ? {
              decision: decisions[0].decision,
              reason: decisions[0].reason || undefined,
              decisionAt: decisions[0].decisionAt.toISOString(),
            }
          : undefined,
    };

    return { success: true, application: result };
  } catch (error) {
    console.error("Error fetching application:", error);
    return { success: false, error: "Failed to fetch application" };
  }
}

/**
 * Admin function: Get all applications for a listing
 */
export async function getApplicationsForListing(listingId: number): Promise<{
  success: boolean;
  applications?: ApplicationWithDetails[];
  error?: string;
}> {
  try {
    // TODO: Add admin authorization check

    const applications = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,

        // Listing details
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,

        // Applicant details
        firstName: individual.firstName,
        lastName: individual.lastName,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applications.listingId, listingId))
      .orderBy(desc(h_applications.id));

    // Get documents for each application
    const applicationIds = applications.map((app) => app.id);
    const documents =
      applicationIds.length > 0
        ? await db
            .select()
            .from(h_applicationDocuments)
            .where(
              inArray(h_applicationDocuments.applicationId, applicationIds)
            )
        : [];

    // Get latest decisions for each application
    const decisions =
      applicationIds.length > 0
        ? await db
            .select({
              applicationId: h_applicationDecisions.applicationId,
              decision: h_applicationDecisions.decision,
              reason: h_applicationDecisions.reason,
              decisionAt: h_applicationDecisions.decisionAt,
            })
            .from(h_applicationDecisions)
            .where(
              inArray(h_applicationDecisions.applicationId, applicationIds)
            )
            .orderBy(desc(h_applicationDecisions.decisionAt))
        : [];

    // Combine data
    const result: ApplicationWithDetails[] = applications.map((app) => ({
      id: app.id,
      applicantId: app.applicantId,
      listingId: app.listingId,
      applicationDetails: app.applicationDetails
        ? JSON.parse(app.applicationDetails)
        : {},
      createdAt: new Date().toISOString(), // TODO: Add proper timestamp

      applicant: {
        id: app.applicantId,
        firstName: app.firstName || "",
        lastName: app.lastName || "",
        email: "", // TODO: Get from contact points
      },
      listing: {
        id: app.listingId,
        listingType: app.listingType,
        listingDetails: app.listingDetails
          ? JSON.parse(app.listingDetails)
          : {},
      },
      documents: documents
        .filter((doc) => doc.applicationId === app.id)
        .map((doc) => ({
          id: doc.id,
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          uploadedAt: doc.uploadedAt.toISOString(),
        })),
      latestDecision: decisions.find((dec) => dec.applicationId === app.id)
        ? {
            decision: decisions.find((dec) => dec.applicationId === app.id)!
              .decision,
            reason:
              decisions.find((dec) => dec.applicationId === app.id)!.reason ||
              undefined,
            decisionAt: decisions
              .find((dec) => dec.applicationId === app.id)!
              .decisionAt.toISOString(),
          }
        : undefined,
    }));

    return { success: true, applications: result };
  } catch (error) {
    console.error("Error fetching applications for listing:", error);
    return { success: false, error: "Failed to fetch applications" };
  }
}

/**
 * Admin function: Make a decision on an application
 */
export async function makeApplicationDecision(
  applicationId: number,
  decision: "approved" | "rejected",
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    // TODO: Add admin authorization check

    // Verify the application exists
    const application = await db
      .select()
      .from(h_applications)
      .where(eq(h_applications.id, applicationId))
      .limit(1);

    if (application.length === 0) {
      return { success: false, error: "Application not found" };
    }

    // Get reviewer party ID (admin user making the decision)
    const reviewerPartyId = userAttributes["custom:db_id"];
    if (!reviewerPartyId) {
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Insert decision
    await db.insert(h_applicationDecisions).values({
      applicationId,
      decision,
      reason: reason || null,
      reviewerId: parseInt(reviewerPartyId),
      decisionAt: new Date(),
    });

    return { success: true };
  } catch (error) {
    console.error("Error making application decision:", error);
    return { success: false, error: "Failed to make decision" };
  }
}

/**
 * Update document verification status
 */
export async function updateDocumentStatus(
  documentId: number,
  status: "verified" | "rejected"
): Promise<{ success: boolean; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    // TODO: Add admin authorization check

    // Verify the document exists
    const document = await db
      .select()
      .from(h_applicationDocuments)
      .where(eq(h_applicationDocuments.id, documentId))
      .limit(1);

    if (document.length === 0) {
      return { success: false, error: "Document not found" };
    }

    // Get reviewer party ID (admin user making the decision)
    const reviewerPartyId = userAttributes["custom:db_id"];
    if (!reviewerPartyId) {
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Insert document status
    await db.insert(h_applicationDocumentsStatus).values({
      applicationDocumentId: documentId,
      status,
      statusAt: new Date(),
      statusBy: parseInt(reviewerPartyId),
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating document status:", error);
    return { success: false, error: "Failed to update document status" };
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStats(): Promise<{
  success: boolean;
  stats?: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    byListingType: Record<string, number>;
  };
  error?: string;
}> {
  try {
    // TODO: Add admin authorization check

    // Get total applications
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(h_applications);

    // Get applications by latest decision status
    const statusStats = await db
      .select({
        decision: h_applicationDecisions.decision,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .leftJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .groupBy(h_applicationDecisions.decision);

    // Get applications by listing type
    const typeStats = await db
      .select({
        listingType: h_listings.listingType,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .groupBy(h_listings.listingType);

    const stats = {
      total: totalResult[0]?.count || 0,
      pending:
        statusStats.find((s) => s.decision === null || s.decision === "pending")
          ?.count || 0,
      approved: statusStats.find((s) => s.decision === "approved")?.count || 0,
      rejected: statusStats.find((s) => s.decision === "rejected")?.count || 0,
      byListingType: typeStats.reduce(
        (acc, stat) => {
          acc[stat.listingType] = stat.count;
          return acc;
        },
        {} as Record<string, number>
      ),
    };

    return { success: true, stats };
  } catch (error) {
    console.error("Error fetching application stats:", error);
    return { success: false, error: "Failed to fetch application statistics" };
  }
}
