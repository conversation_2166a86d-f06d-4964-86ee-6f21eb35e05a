"use server";

import { db } from "../db";
import { eq, desc, and, inArray, sql } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  vehicleMedia,
} from "../drizzle/schema";
import {
  h_listings,
  h_listing_approval_status,
  h_listing_publish_status,
} from "../drizzle/h_schema/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Get user's vehicles for listing selection
export async function getUserVehiclesForListing() {
  const userAttributes = await getUserAttributes();

  if (!userAttributes) {
    throw new Error("User not authenticated");
  }

  const partyId = userAttributes["custom:db_id"];

  if (!partyId) {
    throw new Error("User party ID not found");
  }

  // Get vehicles with media in a single query using LEFT JOIN
  const results = await db
    .select({
      // Vehicle fields
      vehicleId: vehicles.id,
      partyId: vehicles.partyId,
      modelId: vehicles.modelId,
      vinNumber: vehicles.vinNumber,
      vehicleRegistration: vehicles.vehicleRegistration,
      manufacturingYear: vehicles.manufacturingYear,
      color: vehicles.color,
      isActive: vehicles.isActive,
      createdAt: vehicles.createdAt,
      // Model details
      modelId_: vehicleModel.id,
      modelName: vehicleModel.model,
      modelMakeId: vehicleModel.makeId,
      makeId: vehicleMake.id,
      makeName: vehicleMake.name,
      // Media fields (will be null if no media)
      mediaId: vehicleMedia.id,
      mediaPath: vehicleMedia.mediaPath,
      mediaCreatedAt: vehicleMedia.createdAt,
    })
    .from(vehicles)
    .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
    .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
    .leftJoin(vehicleMedia, eq(vehicles.id, vehicleMedia.vehicleId))
    .where(eq(vehicles.partyId, parseInt(partyId)))
    .orderBy(desc(vehicles.createdAt));

  // Group results by vehicle and collect media
  const vehicleMap = new Map();

  results.forEach((row) => {
    const vehicleId = row.vehicleId;

    if (!vehicleMap.has(vehicleId)) {
      vehicleMap.set(vehicleId, {
        id: row.vehicleId,
        partyId: row.partyId,
        modelId: row.modelId,
        vinNumber: row.vinNumber,
        vehicleRegistration: row.vehicleRegistration,
        manufacturingYear: row.manufacturingYear,
        color: row.color,
        isActive: row.isActive,
        model: {
          id: row.modelId_,
          model: row.modelName,
          makeId: row.modelMakeId,
          make: {
            id: row.makeId,
            name: row.makeName,
          },
        },
        media: [],
      });
    }

    // Add media if it exists
    if (row.mediaId) {
      vehicleMap.get(vehicleId).media.push({
        id: row.mediaId,
        vehicleId,
        mediaPath: row.mediaPath,
        createdAt: row.mediaCreatedAt?.toString() || "",
      });
    }
  });

  return Array.from(vehicleMap.values());
}

// Determine listing source type based on URL path
export async function determineListingSourceType(
  pathname: Promise<string>
): Promise<"vehicle" | "catalog"> {
  return (await pathname).includes("/admin/") ? "catalog" : "vehicle";
}

// Determine listing type and prepare listing details
export async function prepareListingTypeAndDetails(
  formData: any,
  sourceType: "vehicle" | "catalog"
): Promise<{
  listingType: string;
  listingDetails: any;
}> {
  // For catalog listings (admin only)
  if (sourceType === "catalog") {
    if (formData.listingType === "ehailing-platform") {
      return {
        listingType: "ehailing-platform",
        listingDetails: {
          rate: formData.rate || 2700, // Default weekly rate
          type: "weekly", // Default payment frequency
          initiationFee: formData.initiationFee || 7500, // Default initiation fee
          // Additional e-hailing specific fields
          driverExperienceRequired: formData.driverExperienceRequired || 1, // Years
          minimumAge: formData.minimumAge || 21,
          preferredGender: formData.preferredGender || null, // Optional preference
          preferredLocation: formData.preferredLocation || null,
        },
      };
    }

    // Other admin catalog listing types can be added here
    // Default to rental for other catalog types
    return {
      listingType: "rental",
      listingDetails: {
        // Default catalog listing details
        rate: formData.price || 0,
        type: "monthly",
        purpose: "business",
      },
    };
  }

  // For vehicle listings (regular users)
  if (formData.listingType === "rental") {
    return {
      listingType: "rental",
      listingDetails: {
        rate: parseFloat(formData.rentalRate || "0"),
        type: formData.rentalPeriod || "monthly",
        purpose: formData.rentalPurpose || "individual",
      },
    };
  } else if (formData.listingType === "fractional") {
    return {
      listingType: "fractional",
      listingDetails: {
        fraction: parseFloat(formData.ownershipPercentage || "0") / 100, // Convert percentage to decimal
        amount: parseFloat(formData.pricePerFraction || "0"),
        allowPartialPurchase: formData.allowPartialPurchase || false,
        minimumPurchasePercentage: formData.allowPartialPurchase
          ? parseFloat(formData.minimumPurchasePercentage || "0") / 100
          : null,
      },
    };
  }

  // Default case
  throw new Error("Invalid listing type");
}

// Create a listing from the drawer form data
export async function createListingFromDrawerData(
  formData: any,
  pathname: Promise<string>
) {
  const userAttributes = await getUserAttributes();

  if (!userAttributes) {
    throw new Error("User not authenticated");
  }

  const partyId = userAttributes["custom:db_id"];

  if (!partyId) {
    throw new Error("User party ID not found");
  }

  // Determine source type based on URL path
  const sourceType = await determineListingSourceType(pathname);

  // Get listing type and details
  const { listingType, listingDetails } = await prepareListingTypeAndDetails(
    formData,
    sourceType
  );

  return await db.transaction(async (tx) => {
    try {
      // Get the source ID based on source type
      let sourceId: number;

      if (sourceType === "vehicle") {
        // For vehicle listings, use the selected vehicle ID
        sourceId = formData.selectedVehicleId;

        if (!sourceId) {
          throw new Error("Vehicle ID is required for vehicle listings");
        }

        // Verify vehicle exists and belongs to user
        const vehicleExists = await tx
          .select({ id: vehicles.id })
          .from(vehicles)
          .where(
            and(
              eq(vehicles.id, sourceId),
              eq(vehicles.partyId, parseInt(partyId))
            )
          )
          .limit(1);

        if (vehicleExists.length === 0) {
          throw new Error(
            `Vehicle with ID ${sourceId} does not exist or does not belong to user`
          );
        }
      } else {
        // For catalog listings, use the selected catalog item ID
        sourceId = formData.catalogItemId;

        if (!sourceId) {
          throw new Error("Catalog item ID is required for catalog listings");
        }

        // Verify catalog item exists (admin only)
        // This would need to be implemented based on your schema
      }

      // Create the listing
      const newListing = await tx
        .insert(h_listings)
        .values({
          partyId: parseInt(partyId),
          sourceType: sourceType,
          sourceId: sourceId,
          listingType: listingType as any,
          effectiveFrom: new Date().toISOString(),
          effectiveTo: new Date(
            Date.now() + 90 * 24 * 60 * 60 * 1000
          ).toISOString(), // 90 days from now
          listingDetails: JSON.stringify(listingDetails),
        })
        .returning();

      // Create initial approval status (pending)
      await tx.insert(h_listing_approval_status).values({
        listingId: newListing[0].id,
        status: "pending",
        statusAt: new Date().toISOString(),
        statusBy: parseInt(partyId),
      });

      // Create initial publish status (draft)
      await tx.insert(h_listing_publish_status).values({
        listingId: newListing[0].id,
        status: "pending",
        statusAt: new Date().toISOString(),
        statusBy: parseInt(partyId),
      });

      // Add documents if provided
      if (formData.documents && formData.documents.length > 0) {
        // This would need to be implemented based on your schema
        // For example:
        // const documentInserts = formData.documents.map(doc => ({
        //   listingId: newListing[0].id,
        //   documentType: doc.type,
        //   documentPath: doc.path,
        //   createdAt: new Date().toISOString(),
        // }));
        // await tx.insert(listingDocuments).values(documentInserts);
      }

      return {
        success: true,
        listing: newListing[0],
        sourceType: sourceType,
        sourceId: sourceId,
      };
    } catch (error) {
      console.error("Error in createListingFromDrawerData transaction:", error);
      throw error;
    }
  });
}

// Function to check if user has vehicles
export async function checkUserHasVehicles(): Promise<boolean> {
  const userAttributes = await getUserAttributes();

  if (!userAttributes) {
    throw new Error("User not authenticated");
  }

  const partyId = userAttributes["custom:db_id"];

  if (!partyId) {
    throw new Error("User party ID not found");
  }

  // Count vehicles owned by the user
  const vehicleCount = await db
    .select({ count: sql<number>`count(*)` })
    .from(vehicles)
    .where(eq(vehicles.partyId, parseInt(partyId)));

  return vehicleCount[0].count > 0;
}
