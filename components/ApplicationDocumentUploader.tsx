"use client";

import { useRef, useState } from "react";
import { Upload, FileText, CheckCircle, Camera, MapPin } from "lucide-react";
import { DocumentUpload } from "@/lib/utils";
import { uploadApplicationDocumentsAction } from "@/actions/applications";

export interface UploadedDocument {
  name: string;
  documentType: string;
  file?: File;
  s3Path?: string;
  uploaded: boolean;
  required: boolean;
  isSpecial?: boolean;
}

interface ProofOfResidenceState {
  hasFormalDocument: boolean | null;
  location: string | null;
  affidavit: File | null;
}

interface ApplicationDocumentUploaderProps {
  applicationType: "ehailing" | "rental" | "fractional";
  applicationId?: number;
  documents: UploadedDocument[];
  setDocuments: (documents: UploadedDocument[]) => void;
  onUploadComplete?: () => void;
  onError?: (error: string) => void;
}

export default function ApplicationDocumentUploader({
  applicationType,
  applicationId,
  documents,
  setDocuments,
  onUploadComplete,
  onError,
}: ApplicationDocumentUploaderProps) {
  const cameraRef = useRef<HTMLInputElement | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState<string | null>(
    null
  );
  const [proofOfResidence, setProofOfResidence] =
    useState<ProofOfResidenceState>({
      hasFormalDocument: null,
      location: null,
      affidavit: null,
    });

  // Handle camera capture for selfie
  const handleCameraCapture = () => {
    if (cameraRef.current) {
      cameraRef.current.click();
    }
  };

  // Handle location capture for proof of residence
  const handleLocationCapture = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = `${position.coords.latitude},${position.coords.longitude}`;
          setProofOfResidence((prev) => ({
            ...prev,
            location,
          }));
        },
        (error) => {
          console.error("Error getting location:", error);
          onError?.("Failed to capture location. Please try again.");
        }
      );
    } else {
      onError?.("Geolocation is not supported by this browser.");
    }
  };

  // Handle document upload
  const handleDocumentUpload = async (index: number, file: File) => {
    setIsUploading(true);
    setUploadingDocument(documents[index].documentType);

    try {
      // Upload file to S3
      const uploadResult = await DocumentUpload(file, "applications");
      const s3Path = uploadResult?.path || uploadResult;

      // Update document state
      const updatedDocuments = [...documents];
      updatedDocuments[index] = {
        ...updatedDocuments[index],
        file,
        s3Path: typeof s3Path === "string" ? s3Path : "",
        uploaded: true,
      };
      setDocuments(updatedDocuments);

      // If we have an applicationId, save to database
      if (applicationId && s3Path) {
        await uploadApplicationDocumentsAction(applicationId, [
          {
            documentType: documents[index].documentType,
            documentUrl: typeof s3Path === "string" ? s3Path : "",
          },
        ]);
      }

      onUploadComplete?.();
    } catch (error) {
      console.error("Upload failed:", error);
      onError?.("Failed to upload document. Please try again.");
    } finally {
      setIsUploading(false);
      setUploadingDocument(null);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center mb-3">
          <FileText size={18} className="mr-2 text-[#009639]" />
          <h4 className="font-semibold text-[#333333]">Upload Documents</h4>
        </div>
        <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
          <div className="space-y-6">
            {documents.map((doc, index) => (
              <div key={index} className="rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-[#333333]">
                      {doc.name}
                    </span>
                    {/* Always show as required visually */}
                    <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                      Required
                    </span>
                  </div>
                  {doc.uploaded && (
                    <CheckCircle size={16} className="text-green-500" />
                  )}
                </div>

                {/* Special handling for Proof of Residence */}
                {doc.name === "Proof of residence" && !doc.uploaded ? (
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-[#333333] mb-2 block">
                        Do you have a formal proof of residence document?
                      </label>
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="hasFormalDocument"
                            checked={
                              proofOfResidence.hasFormalDocument === true
                            }
                            onChange={() =>
                              setProofOfResidence((prev) => ({
                                ...prev,
                                hasFormalDocument: true,
                              }))
                            }
                            className="mr-2"
                          />
                          <span className="text-sm text-[#333333]">Yes</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="hasFormalDocument"
                            checked={
                              proofOfResidence.hasFormalDocument === false
                            }
                            onChange={() =>
                              setProofOfResidence((prev) => ({
                                ...prev,
                                hasFormalDocument: false,
                              }))
                            }
                            className="mr-2"
                          />
                          <span className="text-sm text-[#333333]">No</span>
                        </label>
                      </div>
                    </div>

                    {proofOfResidence.hasFormalDocument ? (
                      <div>
                        <label className="text-xs text-gray-600 mb-2 block">
                          Upload proof of residence (not older than 3 months)
                        </label>
                        <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                          <div className="text-center">
                            <Upload
                              size={20}
                              className="mx-auto mb-1 text-gray-400"
                            />
                            <span className="text-xs text-gray-500">
                              Click to upload
                            </span>
                          </div>
                          <input
                            type="file"
                            className="hidden"
                            accept=".pdf,.jpg,.jpeg,.png"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleDocumentUpload(index, file);
                              }
                            }}
                          />
                        </label>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div>
                          <label className="text-xs text-gray-600 mb-2 block">
                            Provide your current pin location
                          </label>
                          <button
                            onClick={handleLocationCapture}
                            className="flex items-center justify-center w-full h-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-[#009639] transition-colors"
                          >
                            <MapPin size={16} className="mr-2 text-gray-400" />
                            <span className="text-xs text-gray-500">
                              {proofOfResidence.location
                                ? "Location captured"
                                : "Capture location"}
                            </span>
                          </button>
                        </div>
                        <div>
                          <label className="text-xs text-gray-600 mb-2 block">
                            Upload affidavit from landlord
                          </label>
                          <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                            <div className="text-center">
                              <Upload
                                size={20}
                                className="mx-auto mb-1 text-gray-400"
                              />
                              <span className="text-xs text-gray-500">
                                Upload affidavit
                              </span>
                            </div>
                            <input
                              type="file"
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  setProofOfResidence((prev) => ({
                                    ...prev,
                                    affidavit: file,
                                  }));
                                  handleDocumentUpload(index, file);
                                }
                              }}
                            />
                          </label>
                        </div>
                      </div>
                    )}
                  </div>
                ) : doc.name === "Selfie" && !doc.uploaded ? (
                  /* Special handling for Selfie - Camera only */
                  <div className="space-y-3">
                    <label className="text-xs text-gray-600 mb-2 block">
                      Take a selfie using your camera
                    </label>
                    <button
                      onClick={handleCameraCapture}
                      className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg hover:border-[#009639] transition-colors"
                    >
                      <div className="text-center">
                        <Camera
                          size={20}
                          className="mx-auto mb-1 text-gray-400"
                        />
                        <span className="text-xs text-gray-500">
                          Take selfie
                        </span>
                      </div>
                    </button>
                    {/* Hidden camera input */}
                    <input
                      ref={cameraRef}
                      type="file"
                      className="hidden"
                      accept="image/*"
                      capture="user"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          handleDocumentUpload(index, file);
                        }
                      }}
                    />
                  </div>
                ) : !doc.uploaded ? (
                  /* Regular document upload */
                  <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                    <div className="text-center">
                      <Upload
                        size={20}
                        className="mx-auto mb-1 text-gray-400"
                      />
                      <span className="text-xs text-gray-500">
                        Click to upload
                      </span>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          handleDocumentUpload(index, file);
                        }
                      }}
                    />
                  </label>
                ) : (
                  /* Document uploaded state */
                  <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-2">
                    <span className="text-xs text-green-700">
                      {doc.file?.name || "Document uploaded"}
                    </span>
                    <button
                      onClick={() => {
                        const updatedDocuments = [...documents];
                        updatedDocuments[index] = {
                          ...updatedDocuments[index],
                          uploaded: false,
                          file: undefined,
                        };
                        setDocuments(updatedDocuments);
                      }}
                      className="text-xs text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
