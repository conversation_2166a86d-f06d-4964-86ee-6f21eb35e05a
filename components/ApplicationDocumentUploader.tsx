"use client";

import { useRef, useState } from "react";
import { Upload, X, FileTex<PERSON>, Check<PERSON>ircle, AlertCircle, Loader2 } from "lucide-react";
import { DocumentUpload, DocumentDelete } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { uploadApplicationDocumentsAction } from "@/actions/applications";
import {
  EHAILING_DOCUMENT_TYPES,
  RENTAL_DOCUMENT_TYPES,
  FRACTIONAL_DOCUMENT_TYPES,
  type EhailingDocumentType,
  type RentalDocumentType,
  type FractionalDocumentType,
} from "@/types/applications";

export interface UploadedDocument {
  documentType: string;
  file: File;
  s3Path: string;
  uploaded: boolean;
  required: boolean;
  isSpecial?: boolean;
}

interface ApplicationDocumentUploaderProps {
  applicationType: "ehailing" | "rental" | "fractional";
  applicationId?: number;
  documents: UploadedDocument[];
  onDocumentsChange: (documents: UploadedDocument[]) => void;
  onUploadComplete?: () => void;
  onError?: (error: string) => void;
}

export default function ApplicationDocumentUploader({
  applicationType,
  applicationId,
  documents,
  onDocumentsChange,
  onUploadComplete,
  onError,
}: ApplicationDocumentUploaderProps) {
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});
  const [isUploading, setIsUploading] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState<string | null>(null);

  // Get document types based on application type
  const getDocumentTypes = () => {
    switch (applicationType) {
      case "ehailing":
        return EHAILING_DOCUMENT_TYPES;
      case "rental":
        return RENTAL_DOCUMENT_TYPES;
      case "fractional":
        return FRACTIONAL_DOCUMENT_TYPES;
      default:
        return EHAILING_DOCUMENT_TYPES;
    }
  };

  // Initialize documents if not provided
  const initializeDocuments = () => {
    if (documents.length === 0) {
      const documentTypes = getDocumentTypes();
      const initialDocuments: UploadedDocument[] = documentTypes.map((docType) => ({
        documentType: docType,
        file: null as any,
        s3Path: "",
        uploaded: false,
        required: true,
        isSpecial: docType === "Proof of residence" || docType === "Selfie",
      }));
      onDocumentsChange(initialDocuments);
      return initialDocuments;
    }
    return documents;
  };

  const currentDocuments = initializeDocuments();

  const handleError = (message: string) => {
    console.error(message);
    onError?.(message);
  };

  const handleFileUpload = async (documentType: string, file: File) => {
    setUploadingDocument(documentType);
    
    try {
      // Upload to S3
      const uploadResult = await DocumentUpload(file, "applications");
      
      if (!uploadResult || !uploadResult.path) {
        throw new Error("Failed to upload document to S3");
      }

      // Update document in the list
      const updatedDocuments = currentDocuments.map((doc) =>
        doc.documentType === documentType
          ? {
              ...doc,
              file,
              s3Path: uploadResult.path,
              uploaded: true,
            }
          : doc
      );

      onDocumentsChange(updatedDocuments);

      // If we have an applicationId, save to database immediately
      if (applicationId) {
        await uploadApplicationDocumentsAction(applicationId, [
          {
            documentType,
            documentUrl: uploadResult.path,
          },
        ]);
      }

      return uploadResult.path;
    } catch (error) {
      console.error("Error uploading document:", error);
      handleError(`Failed to upload ${documentType}. Please try again.`);
      throw error;
    } finally {
      setUploadingDocument(null);
    }
  };

  const handleFileChange = async (
    documentType: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg", "application/pdf"];
    if (!allowedTypes.includes(file.type)) {
      handleError("Please upload only JPG, PNG, or PDF files");
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      handleError("File size must be less than 10MB");
      return;
    }

    try {
      await handleFileUpload(documentType, file);
    } catch (error) {
      // Error already handled in handleFileUpload
    }
  };

  const handleRemoveDocument = async (documentType: string) => {
    const document = currentDocuments.find((doc) => doc.documentType === documentType);
    if (!document || !document.s3Path) return;

    try {
      // Delete from S3
      await DocumentDelete(document.s3Path);

      // Update document in the list
      const updatedDocuments = currentDocuments.map((doc) =>
        doc.documentType === documentType
          ? {
              ...doc,
              file: null as any,
              s3Path: "",
              uploaded: false,
            }
          : doc
      );

      onDocumentsChange(updatedDocuments);
    } catch (error) {
      console.error("Error removing document:", error);
      handleError(`Failed to remove ${documentType}`);
    }
  };

  const uploadAllDocuments = async () => {
    if (!applicationId) {
      handleError("Application ID is required to upload documents");
      return;
    }

    setIsUploading(true);
    
    try {
      const documentsToUpload = currentDocuments
        .filter((doc) => doc.uploaded && doc.s3Path)
        .map((doc) => ({
          documentType: doc.documentType,
          documentUrl: doc.s3Path,
        }));

      if (documentsToUpload.length === 0) {
        handleError("No documents to upload");
        return;
      }

      const result = await uploadApplicationDocumentsAction(applicationId, documentsToUpload);
      
      if (result.success) {
        onUploadComplete?.();
      } else {
        handleError(result.error || "Failed to upload documents");
      }
    } catch (error) {
      console.error("Error uploading all documents:", error);
      handleError("Failed to upload documents. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const getUploadedCount = () => {
    return currentDocuments.filter((doc) => doc.uploaded).length;
  };

  const getTotalRequired = () => {
    return currentDocuments.filter((doc) => doc.required).length;
  };

  return (
    <div className="space-y-4">
      {/* Progress indicator */}
      <div className="bg-white rounded-lg p-4 border border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Documents Uploaded
          </span>
          <span className="text-sm text-gray-500">
            {getUploadedCount()} of {getTotalRequired()}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-[#009639] h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(getUploadedCount() / getTotalRequired()) * 100}%`,
            }}
          />
        </div>
      </div>

      {/* Document upload cards */}
      <div className="space-y-3">
        {currentDocuments.map((document) => (
          <Card key={document.documentType} className="border border-gray-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {document.uploaded ? (
                      <CheckCircle size={20} className="text-[#009639]" />
                    ) : uploadingDocument === document.documentType ? (
                      <Loader2 size={20} className="text-[#009639] animate-spin" />
                    ) : (
                      <FileText size={20} className="text-gray-400" />
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {document.documentType}
                      {document.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </p>
                    {document.uploaded && (
                      <p className="text-xs text-gray-500">
                        {document.file?.name}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {document.uploaded ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveDocument(document.documentType)}
                      disabled={uploadingDocument === document.documentType}
                    >
                      <X size={16} />
                    </Button>
                  ) : (
                    <>
                      <input
                        ref={(el) => {
                          fileInputRefs.current[document.documentType] = el;
                        }}
                        type="file"
                        accept="image/*,.pdf"
                        onChange={(e) => handleFileChange(document.documentType, e)}
                        className="hidden"
                        disabled={uploadingDocument === document.documentType}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          fileInputRefs.current[document.documentType]?.click()
                        }
                        disabled={uploadingDocument === document.documentType}
                      >
                        <Upload size={16} className="mr-1" />
                        Upload
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Upload all button (only show if applicationId is provided) */}
      {applicationId && getUploadedCount() > 0 && (
        <Button
          onClick={uploadAllDocuments}
          disabled={isUploading}
          className="w-full bg-[#009639] hover:bg-[#007A2F]"
        >
          {isUploading ? (
            <>
              <Loader2 size={16} className="mr-2 animate-spin" />
              Uploading Documents...
            </>
          ) : (
            `Upload ${getUploadedCount()} Document${getUploadedCount() !== 1 ? 's' : ''}`
          )}
        </Button>
      )}

      {/* Help text */}
      <Alert>
        <AlertCircle size={16} />
        <AlertDescription className="text-sm">
          Upload clear, high-quality images or PDF files. Maximum file size is 10MB.
          All required documents must be uploaded before submitting your application.
        </AlertDescription>
      </Alert>
    </div>
  );
}
