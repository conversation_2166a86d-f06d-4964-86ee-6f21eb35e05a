"use client";

import { SWRConfig } from 'swr';
import { ReactNode } from 'react';

interface SWRProviderProps {
  children: ReactNode;
}

export default function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig
      value={{
        // Global SWR configuration
        revalidateOnFocus: false,
        revalidateOnReconnect: true,
        refreshInterval: 0, // Disable global refresh interval (set per hook)
        errorRetryCount: 3,
        errorRetryInterval: 5000,
        dedupingInterval: 2000,
        focusThrottleInterval: 5000,
        onError: (error, key) => {
          console.error(`SWR Error for key "${key}":`, error);
        },
        onSuccess: (data, key, config) => {
          console.log(`SWR Success for key "${key}"`);
        }
      }}
    >
      {children}
    </SWRConfig>
  );
} 