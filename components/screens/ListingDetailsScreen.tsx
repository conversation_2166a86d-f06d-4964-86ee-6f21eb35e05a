"use client";

import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Car, 
  DollarSign, 
  Loader,
  CheckCircle,
  AlertTriangle,
  Calendar as CalendarIcon,
  MapPin,
  FileText,
  Users,
  Clock,
  Info
} from 'lucide-react';
import { useNavigation } from '@/hooks/useNavigation';
import { PageWithScroll } from '@/components/ui/scroll-container';
import { getListingByIdDrizzle, type ListingRead } from '@/drizzle-actions/listings';
import { getVehicleByIdWithListings } from '@/drizzle-actions/vehicle-dashboard';
import { getUrl } from "aws-amplify/storage";
import type { VehicleReadWithListings, VehicleMediaRead } from '@/types/vehicles';
import type { ConditionEnum, AudienceEnum, ListingTypeEnum } from '@/types/listings';


// Application Process Drawer Components
import ApplicationProcessDrawer from '@/app/(main)/home/<USER>/ApplicationProcessDrawer';
import ApplicationSubmittedDrawer from '@/app/(main)/home/<USER>/ApplicationSubmittedDrawer';

// Vehicle interface compatible with application flow
interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  image?: string;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
  leaseTerms?: {
    ownershipTimeline: string;
    paymentOptions: string[];
  };
}

// Read-only image carousel component based on VehicleStatusScreen's VehicleImageCarousel
function ListingImageViewer({ images }: { images: string[] }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [fullScreenIndex, setFullScreenIndex] = useState(0);

  // Touch handling for swipe gestures
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && images.length > 1) {
      setFullScreenIndex((prev) => (prev + 1) % images.length);
    }
    if (isRightSwipe && images.length > 1) {
      setFullScreenIndex((prev) => (prev - 1 + images.length) % images.length);
    }
  };

  // Handle keyboard events for full-screen mode
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isFullScreen) return;
      
      switch (event.key) {
        case 'Escape':
          setIsFullScreen(false);
          break;
        case 'ArrowLeft':
          setFullScreenIndex((prev) => (prev - 1 + images.length) % images.length);
          break;
        case 'ArrowRight':
          setFullScreenIndex((prev) => (prev + 1) % images.length);
          break;
      }
    };

    if (isFullScreen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isFullScreen, images.length]);

  const openFullScreen = (index: number) => {
    setFullScreenIndex(index);
    setIsFullScreen(true);
  };

  const closeFullScreen = () => {
    setIsFullScreen(false);
  };

  if (!images || images.length === 0) {
    return (
      <div className="relative h-48 bg-[#f2f2f2] rounded-xl flex items-center justify-center">
        <Car size={48} className="text-[#797879]" />
      </div>
    );
  }

  return (
    <>
      {/* Regular Carousel */}
      <div 
        className="relative h-48 bg-[#f2f2f2] rounded-xl overflow-hidden"
        onTouchStart={(e) => {
          setTouchEnd(null);
          setTouchStart(e.targetTouches[0].clientX);
        }}
        onTouchMove={(e) => {
          setTouchEnd(e.targetTouches[0].clientX);
        }}
        onTouchEnd={() => {
          if (!touchStart || !touchEnd) return;
          
          const distance = touchStart - touchEnd;
          const isLeftSwipe = distance > minSwipeDistance;
          const isRightSwipe = distance < -minSwipeDistance;

          if (isLeftSwipe && images.length > 1) {
            setCurrentIndex((prev) => (prev + 1) % images.length);
          }
          if (isRightSwipe && images.length > 1) {
            setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
          }
        }}
      >
        <img
          src={images[currentIndex]}
          alt="Vehicle"
          className="w-full h-full object-cover cursor-pointer"
          onClick={() => openFullScreen(currentIndex)}
        />
        {images.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        )}
        
        {/* Fullscreen expand icon - read-only version */}
        <div className="absolute top-2 right-2">
          <button
            className="bg-black/50 text-white p-2 rounded hover:bg-black/70 transition-colors"
            onClick={() => openFullScreen(currentIndex)}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      {/* Full-Screen Modal */}
      {isFullScreen && (
        <div 
          className="fixed inset-0 bg-black z-50 flex items-center justify-center"
          onClick={closeFullScreen}
        >
          {/* Exit fullscreen button */}
          <button
            className="absolute top-4 right-4 text-white bg-black/50 p-2 rounded-full hover:bg-black/70 transition-colors z-10"
            onClick={(e) => {
              e.stopPropagation();
              closeFullScreen();
            }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>

          {/* Image counter */}
          <div 
            className="absolute top-4 left-4 text-white bg-black/50 px-3 py-1 rounded text-sm z-10"
            onClick={(e) => e.stopPropagation()}
          >
            {fullScreenIndex + 1} of {images.length}
          </div>

          {/* Main image */}
          <div 
            className="relative w-full h-full flex items-center justify-center"
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={images[fullScreenIndex]}
              alt={`Vehicle image ${fullScreenIndex + 1}`}
              className="w-full h-full object-contain"
              style={{ maxWidth: '100vw', maxHeight: '100vh' }}
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {/* Scalable thumbnail navigation */}
          {images.length > 1 && (
            <div 
              className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-full max-w-sm px-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div 
                className="flex space-x-1.5 overflow-x-auto scrollbar-hide"
                style={{
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none'
                }}
              >
                {images.map((image, index) => {
                  // Dynamic thumbnail size based on number of images
                  const thumbnailSize = images.length <= 4 ? 'w-12 h-12' : 
                                       images.length <= 8 ? 'w-10 h-10' : 
                                       'w-8 h-8';
                  
                  return (
                    <button
                      key={index}
                      className={`flex-shrink-0 ${thumbnailSize} rounded-md overflow-hidden border-2 transition-all ${
                        index === fullScreenIndex ? "border-white scale-110 shadow-lg" : "border-white/30"
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setFullScreenIndex(index);
                        
                        // Scroll the selected thumbnail into view
                        e.currentTarget.scrollIntoView({
                          behavior: 'smooth',
                          block: 'nearest',
                          inline: 'center'
                        });
                      }}
                    >
                      <img
                        src={image}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  );
                })}
              </div>
              
              {/* Scroll indicators for many images */}
              {images.length > 6 && (
                <div className="flex justify-center mt-2 space-x-1">
                  <div className="w-1 h-1 bg-white/30 rounded-full"></div>
                  <div className="w-1 h-1 bg-white/30 rounded-full"></div>
                  <div className="w-1 h-1 bg-white/30 rounded-full"></div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
}

// Loading skeletons
function VehicleInfoSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
      <div className="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
      <div className="space-y-4">
        {[1,2,3,4,5].map(i => (
          <div key={i}>
            <div className="h-3 bg-gray-200 rounded animate-pulse mb-2 w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded animate-pulse w-full"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

function ListingDetailsSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
      <div className="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
      <div className="space-y-4">
        {[1,2,3,4,5].map(i => (
          <div key={i}>
            <div className="h-3 bg-gray-200 rounded animate-pulse mb-2 w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded animate-pulse w-full"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface ListingDetailsScreenProps {
  listing?: ListingRead;
  vehicle?: VehicleReadWithListings;
  params?: { listingId?: string };
}

export default function ListingDetailsScreen({ 
  listing: listingProp,
  vehicle: vehicleProp,
  params 
}: ListingDetailsScreenProps) {
  const { goBack } = useNavigation();
  const [listing, setListing] = useState<ListingRead | null>(listingProp || null);
  const [vehicle, setVehicle] = useState<VehicleReadWithListings | undefined>(vehicleProp || undefined);
  const [isLoading, setIsLoading] = useState(!listingProp);
  const [error, setError] = useState<string | null>(null);

  // Application flow state
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  // Load listing data
  useEffect(() => {
    if (listingProp) return;
    
    const listingId = params?.listingId;
    if (!listingId) {
      setError('No listing ID provided');
      setIsLoading(false);
      return;
    }

    async function fetchData() {
      try {
        setIsLoading(true);
        
        // Fetch listing details
        const listingData = await getListingByIdDrizzle(parseInt(listingId as string));
        if (!listingData) {
          setError('Listing not found');
          return;
        }
        setListing(listingData);

        // Fetch vehicle details
        const vehicleData = await getVehicleByIdWithListings(listingData.vehicle_id);
        if (!vehicleData) {
          setError('Vehicle not found');
          return;
        }
        setVehicle(vehicleData || undefined);

      } catch (err) {
        console.error('Error loading listing data:', err);
        setError('Failed to load listing details');
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [listingProp, params?.listingId]);

  // Load vehicle images as signed URLs
  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length || !Array.isArray(vehicle.media)) return;

      try {
        const urls: string[] = await Promise.all(
          vehicle.media.map(async (item: VehicleMediaRead) => {
            const result = await getUrl({ path: item.media_path });
            return result.url.toString();
          })
        );

        setImageUrls(urls);
      } catch (error) {
        console.error("Error loading vehicle images:", error);
        setImageUrls([]);
      }
    }

    if (vehicle) {
      loadImages();
    }
  }, [vehicle]);

  const getListingTypeDisplay = (type: ListingTypeEnum) => {
    switch (type) {
      case 'SHORT_TERM_LEASE_OUT':
        return 'Short-term Rental';
      case 'LONG_TERM_LEASE_OUT':
        return 'Long-term Rental';
      case 'CO_OWNERSHIP_SALE':
        return 'Co-ownership Sale';
      default:
        return type;
    }
  };

  const getConditionDisplay = (condition: ConditionEnum) => {
    return condition === 'new' ? 'New' : 'Used';
  };

  const getAudienceDisplay = (audience: AudienceEnum) => {
    switch (audience) {
      case 'BUSINESS':
        return 'Business Use';
      case 'E_HAILING':
        return 'E-hailing';
      case 'CONSUMER':
        return 'Personal Use';
      default:
        return audience;
    }
  };

  // Convert listing data to Vehicle format for application process
  const convertToVehicleFormat = (): Vehicle | null => {
    if (!listing || !vehicle) return null;

    return {
      id: vehicle.id,
      make: vehicle.model?.make?.name || 'Unknown',
      model: vehicle.model?.model || 'Unknown',
      year: vehicle.manufacturing_year || new Date().getFullYear(),
      weeklyRate: listing.asking_price / 4, // Approximate weekly rate from monthly
      image: '/placeholder.svg',
      requirements: {
        minDeposit: listing.asking_price * 0.1, // 10% deposit
        documents: [
          "ID Document",
          "Bank Statement - 3 months",
          "Proof of residence",
          "Driver's license",
          ...(listing.listing_type === 'SHORT_TERM_LEASE_OUT' || listing.listing_type === 'LONG_TERM_LEASE_OUT' 
            ? ["PrDP (Professional driving permit)", "Police clearance certificate"]
            : ["Proof of income", "Credit report"]
          ),
        ],
      },
      leaseTerms: {
        ownershipTimeline: listing.listing_type === 'CO_OWNERSHIP_SALE' ? "Immediate ownership transfer" : "As per lease agreement",
        paymentOptions: ["Flexible payment options available"],
      },
    };
  };

  const handleApplyForListing = () => {
    const vehicleData = convertToVehicleFormat();
    if (vehicleData) {
      setSelectedVehicle(vehicleData);
      setShowApplicationDrawer(true);
    }
  };

  const handleApplicationSubmit = () => {
    console.log("Application submitted for listing:", listing?.id);
    setShowApplicationDrawer(false);
    setShowSubmittedDrawer(true);
  };

  const getApplyButtonText = () => {
    if (!listing) return 'Apply';
    
    switch (listing.listing_type) {
      case 'SHORT_TERM_LEASE_OUT':
        return 'Apply for Short-term Rental';
      case 'LONG_TERM_LEASE_OUT':
        return 'Apply for Long-term Rental';
      case 'CO_OWNERSHIP_SALE':
        return `Apply for ${(listing.fraction * 100).toFixed(1)}% Ownership`;
      default:
        return 'Apply for Listing';
    }
  };

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button
          onClick={goBack}
          className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
        >
          <ArrowLeft size={24} />
        </button>
        <div>
          <h1 className="text-xl font-bold text-white">Listing Details</h1>
          <p className="text-sm text-green-100">
            {listing ? `${vehicle?.model?.make?.name} ${vehicle?.model?.model}` : "Loading..."}
          </p>
        </div>
      </div>

    </div>
  );

  if (error) {
    const errorHeader = (
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button
            onClick={goBack}
            className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div>
            <h1 className="text-xl font-bold text-white">Listing Details</h1>
            <p className="text-sm text-green-100">Error</p>
          </div>
        </div>
      </div>
    );
    
    return (
      <PageWithScroll
        header={errorHeader}
        className="bg-[#f5f5f5]"
      >
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100 text-center">
            <AlertTriangle size={48} className="text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-[#333333] mb-2">Error Loading Listing</h3>
            <p className="text-[#797879] mb-4">{error}</p>
            <button
              onClick={goBack}
              className="bg-[#009639] text-white px-6 py-2 rounded-lg font-medium"
            >
              Go Back
            </button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <>
      <PageWithScroll
        header={header}
        className="bg-[#f5f5f5]"
      >
        <div className="space-y-4 p-4">
          {/* Listing Status Banner 
          {listing && (
            <div className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white p-4 rounded-xl shadow-md">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-lg">{getListingTypeDisplay(listing.listing_type)}</h3>
                  <p className="text-green-100 text-sm">Available for applications</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">R{listing.asking_price.toLocaleString()}</p>
                  <p className="text-green-100 text-sm">
                    {listing.listing_type === 'CO_OWNERSHIP_SALE' 
                      ? `${(listing.fraction * 100).toFixed(1)}% Share`
                      : 'Monthly Rate'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}
          */}

          {/* Vehicle Images */}
          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <Car size={20} className="text-[#009639] mr-2" />
              <h3 className="font-semibold text-[#333333]">Vehicle Photos</h3>
            </div>
            {isLoading ? (
              <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
            ) : (
                             <ListingImageViewer images={imageUrls} />
            )}
          </div>

          {/* Vehicle Information */}
          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <Info size={20} className="text-[#009639] mr-2" />
              <h3 className="font-semibold text-[#333333]">Vehicle Details</h3>
            </div>
            {isLoading ? (
              <VehicleInfoSkeleton />
            ) : vehicle ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-[#797879] text-sm">Make & Model</p>
                    <p className="text-[#333333] font-medium">
                      {vehicle.model?.make?.name} {vehicle.model?.model}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#797879] text-sm">Year</p>
                    <p className="text-[#333333] font-medium">
                      {vehicle.manufacturing_year || 'Not specified'}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#797879] text-sm">Color</p>
                    <p className="text-[#333333] font-medium">
                      {vehicle.color || 'Not specified'}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#797879] text-sm">Registration</p>
                    <p className="text-[#333333] font-medium">
                      {vehicle.vehicle_registration || 'Private'}
                    </p>
                  </div>
                </div>
                
                {listing && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-[#797879] text-sm">Condition</p>
                        <p className="text-[#333333] font-medium">
                          {getConditionDisplay(listing.condition)}
                        </p>
                      </div>
                      <div>
                        <p className="text-[#797879] text-sm">Target Audience</p>
                        <p className="text-[#333333] font-medium">
                          {getAudienceDisplay(listing.audience)}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : null}
          </div>

          {/* Listing Details */}
          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <DollarSign size={20} className="text-[#009639] mr-2" />
              <h3 className="font-semibold text-[#333333]">Listing Information</h3>
            </div>
            {isLoading ? (
              <ListingDetailsSkeleton />
            ) : listing ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-[#797879] text-sm">Type</p>
                    <p className="text-[#333333] font-medium">
                      {getListingTypeDisplay(listing.listing_type)}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#797879] text-sm">Price</p>
                    <p className="text-[#333333] font-medium">
                      R{listing.asking_price.toLocaleString()}
                    </p>
                  </div>
                  {listing.listing_type === "CO_OWNERSHIP_SALE" && (
                    <div>
                      <p className="text-[#797879] text-sm">Ownership Share</p>
                      <p className="text-[#333333] font-medium">
                        {(listing.fraction * 100).toFixed(1)}%
                      </p>
                    </div>
                  )}
                  <div>
                    <p className="text-[#797879] text-sm">Available From</p>
                    <p className="text-[#333333] font-medium">
                      {listing.effective_from ? new Date(listing.effective_from).toLocaleDateString() : "Now"}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#797879] text-sm">Available Until</p>
                    <p className="text-[#333333] font-medium">
                      {listing.effective_to ? new Date(listing.effective_to).toLocaleDateString() : "Open-ended"}
                    </p>
                  </div>
                </div>
              </div>
            ) : null}
          </div>

          {/* Application Requirements */}
          {listing && convertToVehicleFormat() && (
            <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
              <div className="flex items-center mb-4">
                <FileText size={20} className="text-[#009639] mr-2" />
                <h3 className="font-semibold text-[#333333]">Application Requirements</h3>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-[#797879] text-sm mb-2">Required Documents:</p>
                  <div className="space-y-2">
                    {convertToVehicleFormat()?.requirements.documents.map((doc, index) => (
                      <div key={index} className="flex items-center text-sm text-[#333333]">
                        <div className="w-2 h-2 bg-[#009639] rounded-full mr-3"></div>
                        {doc}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="pt-3 border-t border-gray-100">
                  <p className="text-[#797879] text-sm">Estimated Deposit Required:</p>
                  <p className="text-[#333333] font-medium text-lg">
                    R{convertToVehicleFormat()?.requirements.minDeposit.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Application Process Overview */}
          {listing && (
            <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
              <div className="flex items-center mb-4">
                <Clock size={20} className="text-[#009639] mr-2" />
                <h3 className="font-semibold text-[#333333]">Application Process</h3>
              </div>
              <div className="space-y-3">
                {[
                  "Submit application with required documents",
                  "Application review (2-3 business days)",
                  "Background and credit verification",
                  "Approval notification",
                  listing.listing_type === 'CO_OWNERSHIP_SALE' ? "Transfer of ownership" : "Sign agreement & vehicle handover"
                ].map((step, index) => (
                  <div key={index} className="flex items-center text-sm text-[#333333]">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                      {index + 1}
                    </div>
                    {step}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

          {/* Apply Button */}
          {listing && (
            <div className="p-4">
              <button
                onClick={handleApplyForListing}
                className="w-full bg-[#009639] text-white py-4 rounded-xl text-lg font-semibold shadow-lg hover:bg-[#007A2F] transition-colors"
              >
                {getApplyButtonText()}
              </button>
            </div>
          )}
      </PageWithScroll>

      {/* Application Flow Drawers */}
      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={handleApplicationSubmit}
        selectedVehicle={selectedVehicle}
      />

      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
        onViewStatus={() => {
          setShowSubmittedDrawer(false);
          // Navigate to profile or application status
        }}
      />
    </>
  );
} 