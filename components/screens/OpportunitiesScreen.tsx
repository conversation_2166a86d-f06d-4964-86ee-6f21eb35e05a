"use client";

import { useState, useEffect, useMemo } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import Image from "next/image";
import useSWR from 'swr';
import {
  Search,
  Filter,
  ChevronRight,
  Star,
  Briefcase,
  Shield,
  Plus,
  Crown,
  Tag,
  ChevronLeft,
  ChevronDown,
} from "lucide-react";
import { getAllListingsDrizzle } from "@/drizzle-actions/listings";
import {
  getListingInterestCountsDrizzle,
  getUserInterestStatusDrizzle,
} from "@/drizzle-actions/listing-interest";
import ListingImage from "@/components/listing-image";
import ListingInterestButton from "@/components/listing-interest-button";
import { useCurrentUser } from "@/hooks/use-current-user";

// Interface for the actual data structure returned by getAllListingsDrizzle
interface ListingMediaRead {
  id: number;
  listingId: number;
  mediaPath: string;
  createdAt?: string;
  updatedAt?: string;
}

interface EnrichedListing {
  id: number;
  partyId: number;
  vehicleId: number;
  effectiveFrom: string;
  effectiveTo: string;
  fraction: number;
  askingPrice: number;
  condition: string;
  mileage?: number;
  listingType: string;
  audience: string;
  createdAt: string;
  updatedAt: string;
  // Additional enriched fields from the query
  vehicleVin?: string;
  vehicleColor?: string;
  vehicleYear?: number;
  modelName?: string;
  makeName?: string;
  ownerFirstName?: string;
  ownerLastName?: string;
  // Real fields for media and vehicle data
  media?: ListingMediaRead[];
  vehicle?: {
    model?: {
      make?: { name?: string };
      model?: string;
    };
    color?: string;
    manufacturingYear?: number;
    countryOfRegistration?: string;
    vin_number?: string;
    vehicle_registration?: string;
  };
  owner?: {
    first_name?: string;
    last_name?: string;
  };
}

// Sponsored listing interface
interface SponsoredListing {
  id: string;
  title: string;
  make: string;
  model: string;
  year: number;
  color: string;
  price: number;
  listingType: string;
  condition: string;
  location: string;
  rating: number;
  reviewCount: number;
  fraction?: number;
  image: string;
  description: string;
}

interface OpportunitiesScreenProps {
  params?: Record<string, any>;
}

// Helper function to format listing types for display
const formatListingType = (type: string | undefined | null): string => {
  if (!type) {
    return "Unknown Type";
  }
  
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "Short-term Lease";
    case "LONG_TERM_LEASE_OUT":
      return "Long-term Lease";
    case "CO_OWNERSHIP_SALE":
      return "Co-ownership Sale";
    default:
      return type.replace(/_/g, " ");
  }
};

// Helper function to get listing type color
const getListingTypeColor = (type: string | undefined | null): string => {
  if (!type) {
    return "bg-gray-100 text-gray-800";
  }
  
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "bg-blue-100 text-blue-800";
    case "LONG_TERM_LEASE_OUT":
      return "bg-purple-100 text-purple-800";
    case "CO_OWNERSHIP_SALE":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// Fetcher function for SWR
const fetchOpportunitiesData = async ({ 
  currentUserPartyId, 
  sortBy, 
  sortOrder, 
  page = 1, 
  limit = 10 
}: { 
  currentUserPartyId: number | null, 
  sortBy: string, 
  sortOrder: string, 
  page?: number, 
  limit?: number 
}) => {
  if (!currentUserPartyId) {
    throw new Error("User not authenticated");
  }

  console.log('🔍 Fetching listings with params:', { 
    page, 
    limit, 
    sortBy: sortBy === "effectiveFrom" ? "createdAt" : (sortBy as "createdAt" | "askingPrice"), 
    sortOrder,
    currentUserPartyId
  });

  const result = await getAllListingsDrizzle({
    page,
    limit,
    sortBy: sortBy === "effectiveFrom" ? "createdAt" : (sortBy as "createdAt" | "askingPrice"),
    sortOrder: sortOrder as "asc" | "desc",
  });

  console.log('📊 Raw server response:', {
    recordsCount: result.records?.length || 0,
    total: result.total,
    page: result.page,
    limit: result.limit,
    totalPages: result.totalPages,
    firstFewRecords: result.records?.slice(0, 3).map(r => ({ 
      id: r.id, 
      party_id: r.party_id,
      vehicle_id: r.vehicle_id
    })) || []
  });

  // Check for duplicate IDs in the raw server data
  const allIds = result.records?.map((listing) => listing.id) || [];
  const uniqueIds = [...new Set(allIds)];
  if (allIds.length !== uniqueIds.length) {
    console.error('🚨 DUPLICATE IDs DETECTED in server response!', {
      totalRecords: allIds.length,
      uniqueIds: uniqueIds.length,
      allIds,
      duplicates: allIds.filter((id, index) => allIds.indexOf(id) !== index)
    });
  }

  // Map the snake_case data to camelCase for the UI
  const enrichedListings: EnrichedListing[] = result.records.map((record: any) => ({
    id: record.id,
    partyId: record.party_id,
    vehicleId: record.vehicle_id,
    effectiveFrom: record.effective_from,
    effectiveTo: record.effective_to,
    fraction: record.fraction,
    askingPrice: record.asking_price,
    condition: record.condition,
    mileage: record.mileage,
    listingType: record.listing_type,
    audience: record.audience,
    createdAt: record.created_at || "",
    updatedAt: record.updated_at || "",
    vehicle: {
      model: {
        make: { name: record.vehicle?.model?.make?.name || "Unknown Make" },
        model: record.vehicle?.model?.model || "Unknown Model"
      },
      color: record.vehicle?.color || "Unknown",
      manufacturingYear: record.vehicle?.manufacturing_year || 0,
      countryOfRegistration: record.vehicle?.country_of_registration || "ZA"
    },
    media: record.media || [],
    vehicleVin: record.vehicle?.vin_number,
    vehicleColor: record.vehicle?.color,
    vehicleYear: record.vehicle?.manufacturing_year,
    modelName: record.vehicle?.model?.model,
    makeName: record.vehicle?.model?.make?.name,
    ownerFirstName: record.owner?.first_name,
    ownerLastName: record.owner?.last_name
  }));

  // Fetch interest counts for all listings
  const listingIds = result.records.map((listing) => listing.id);
  let interestCounts: Record<number, number> = {};
  let userInterestStatus: Record<number, boolean> = {};

  if (listingIds.length > 0) {
    interestCounts = await getListingInterestCountsDrizzle(listingIds);
    userInterestStatus = await getUserInterestStatusDrizzle(listingIds, currentUserPartyId);
  }

  console.log('🔧 Processed enriched listings:', {
    enrichedCount: enrichedListings.length,
    enrichedIds: enrichedListings.map(l => l.id),
    interestCountsKeys: Object.keys(interestCounts),
    userInterestKeys: Object.keys(userInterestStatus),
    totalCount: result.total || 0,
    totalPages: Math.ceil((result.total || 0) / limit)
  });

  // Check for duplicates in enriched data
  const enrichedIds = enrichedListings.map(l => l.id);
  const uniqueEnrichedIds = [...new Set(enrichedIds)];
  if (enrichedIds.length !== uniqueEnrichedIds.length) {
    console.error('🚨 DUPLICATE IDs in enriched listings!', {
      total: enrichedIds.length,
      unique: uniqueEnrichedIds.length,
      duplicates: enrichedIds.filter((id, index) => enrichedIds.indexOf(id) !== index)
    });
  }

  return {
    listings: enrichedListings,
    interestCounts,
    userInterestStatus,
    totalCount: result.total || 0,
    totalPages: Math.ceil((result.total || 0) / limit)
  };
};

export default function OpportunitiesScreen({ params }: OpportunitiesScreenProps) {
  const { navigate, navigateToFractionPurchase, navigateToListVehicle, navigateToListingManagement, navigateToListingDetails } = useNavigation();
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("vehicles");
  const [sortBy, setSortBy] = useState<"effectiveFrom" | "askingPrice" | "createdAt">("effectiveFrom");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [showSponsoredListing, setShowSponsoredListing] = useState(true);
  const [itemsPerPage] = useState(10);
  const [showFilters, setShowFilters] = useState(false);

  // Mock sponsored listing data from Poolly
  const sponsoredListing: SponsoredListing = {
    id: "sponsored-poolly-1",
    title: "Suzuki Dzire",
    make: "Suzuki",
    model: "Dzire",
    year: 2022,
    color: "Pearl White",
    price: 2600,
    listingType: "SHORT_TERM_LEASE_OUT",
    condition: "Excellent",
    location: "Gauteng, South Africa",
    rating: 4.9,
    reviewCount: 47,
    fraction: 25,
    image: "/SuzukiDzire.webp",
    description: "The Suzuku Dzire is a cousin to the iconic Suzuki Swift. It's an affordable entry-level sedan. If you're looking for a great car for work, you can't go wrong with the Dzire."
  };

  // SWR for data fetching
  const shouldFetch = !userLoading && currentUserPartyId !== undefined;
  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['opportunities-data', currentUserPartyId, sortBy, sortOrder, currentPage, itemsPerPage] : null,
    () => fetchOpportunitiesData({ 
      currentUserPartyId: currentUserPartyId || null, 
      sortBy, 
      sortOrder, 
      page: currentPage, 
      limit: itemsPerPage 
    }),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 30000,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  );

  const vehicleListings = useMemo(() => {
    const listings = data?.listings || [];
    console.log('🎯 Vehicle listings in component:', {
      count: listings.length,
      ids: listings.map(l => l.id),
      isLoading,
      error: error?.message
    });
    return listings;
  }, [data?.listings, isLoading, error]);
  
  const interestCounts = useMemo(() => data?.interestCounts || {}, [data?.interestCounts]);
  const userInterestStatus = useMemo(() => data?.userInterestStatus || {}, [data?.userInterestStatus]);
  const totalPages = useMemo(() => data?.totalPages || 1, [data?.totalPages]);
  const totalCount = useMemo(() => data?.totalCount || 0, [data?.totalCount]);

  const businessOpportunities = [
    {
      id: "biz-1",
      title: "Ride-sharing Partnership",
      description: "Use your vehicle for ride-sharing during idle hours",
      category: "Transportation",
      earnings: "R5,000-12,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: "biz-2",
      title: "Food Delivery Service",
      description: "Partner with local restaurants for food delivery",
      category: "Food & Beverage",
      earnings: "R3,000-8,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: "biz-3",
      title: "Corporate Transportation",
      description: "Provide transportation for corporate clients",
      category: "Business",
      earnings: "R8,000-20,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
  ];

  const partnerServices = [
    {
      id: "partner-1",
      title: "Premium Insurance",
      description: "Comprehensive coverage for shared vehicles",
      provider: "Santam Insurance",
      icon: <Shield size={24} className="text-[#009639]" />,
    },
    {
      id: "partner-2",
      title: "Mobile Maintenance",
      description: "On-demand vehicle maintenance and repairs",
      provider: "AutoZone Mobile Services",
      icon: <Briefcase size={24} className="text-[#009639]" />,
    },
    {
      id: "partner-3",
      title: "Business Consulting",
      description: "Financial and legal advice for vehicle sharing",
      provider: "Standard Bank Business",
      icon: <Star size={24} className="text-[#009639]" />,
    },
  ];

  // Handle page navigation
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Render sponsored listing card
  const renderSponsoredListing = () => (
    <div key="sponsored-poolly-1" className="ride-card mt-4 overflow-hidden drop-shadow-md rounded-xl border border-gray-100 mb-4">
      <div className="h-60 bg-[#f2f2f2] relative">
        <Image
          src={sponsoredListing.image}
          alt={`${sponsoredListing.make} ${sponsoredListing.model}`}
          fill
          className="object-cover"
          onError={(e) => {
            e.currentTarget.src = "/placeholder.svg?height=160&width=320";
          }}
        />
        
        {/* Sponsored Badge */}
        <div className="absolute top-3 left-3 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center shadow-sm">
          <Star size={12} className="mr-1 fill-current" />
          Sponsored
        </div>
        
        {/* Listing Type Badge */}
        <div className={`absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-medium shadow-sm ${getListingTypeColor(sponsoredListing.listingType)}`}>
          <div className="flex items-center">
            <Tag size={12} className="mr-1" />
            {formatListingType(sponsoredListing.listingType)}
          </div>
        </div>
        
        {/* Ownership Percentage */}
        {sponsoredListing.listingType === "CO_OWNERSHIP_SALE" && sponsoredListing.fraction && (
          <div className="absolute bottom-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
            {sponsoredListing.fraction}% Ownership
          </div>
        )}
        
        {/* Rating 
        <div className="absolute bottom-3 left-3 bg-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center">
          <Star size={12} className="text-[#ff5c00] mr-1" />
          <span>{sponsoredListing.rating}</span>
          <span className="text-[#797879] ml-1">({sponsoredListing.reviewCount})</span>
        </div>*/}
      </div>

      <div className="p-4">
        <div className="flex justify-between items-center mb-1">
          <h3 className="text-[#333333] font-semibold">
            {sponsoredListing.make} {sponsoredListing.model}
          </h3>
          <span className="text-lg font-bold text-[#009639]">
            R{sponsoredListing.price.toLocaleString()}
            {(sponsoredListing.listingType === "SHORT_TERM_LEASE_OUT" || sponsoredListing.listingType === "LONG_TERM_LEASE_OUT") && (
              <span className="text-sm font-medium">/month</span>
            )}
          </span>
        </div>

        <p className="text-sm text-[#797879] mb-2">
          {sponsoredListing.color} • {sponsoredListing.year} • {sponsoredListing.condition}
        </p>

        <div className="flex items-center mb-3">
          <div className="text-xs text-[#797879]">
            📍 {sponsoredListing.location}
          </div>
        </div>

        <p className="text-sm text-[#333333] mb-3">
          {sponsoredListing.description}
        </p>

        <button
          className="ride-primary-btn w-full py-2 text-sm"
          onClick={() => {
            // Navigate to a special sponsored listing page or show interest
            navigate('fraction-purchase', { id: sponsoredListing.id, sponsored: true });
          }}
        >
          Apply for a lease
        </button>
      </div>
    </div>
  );

  // Render pagination controls
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pageNumbers = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex items-center justify-center space-x-2 mt-6 mb-4">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="p-2 rounded-lg border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft size={16} />
        </button>
        
        {pageNumbers.map((pageNum) => (
          <button
            key={pageNum}
            onClick={() => handlePageChange(pageNum)}
            className={`px-3 py-2 rounded-lg border ${
              currentPage === pageNum
                ? 'bg-[#009639] text-white border-[#009639]'
                : 'border-gray-200 text-gray-700'
            }`}
          >
            {pageNum}
          </button>
        ))}
        
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="p-2 rounded-lg border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight size={16} />
        </button>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 border-b border-[#007A2F] flex-shrink-0">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold text-white">Opportunities</h1>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={navigateToListVehicle}
          >
            <Plus size={20} className="text-white" />
          </button>
        </div>
      </div>

            {/* Search and Filter */}
      <div className="p-4 flex-shrink-0">
        <div className="flex space-x-2 mb-3">
          <div className="flex-1 bg-white rounded-full px-4 py-2 flex items-center shadow-md border border-gray-100">
            <Search size={18} className="text-[#797879] mr-2" />
            <input
              type="text"
              placeholder="Search opportunities"
              className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {activeTab === "vehicles" && (
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="bg-white p-2 rounded-full shadow-md border border-gray-100 hover:border-[#009639] transition-colors"
            >
              <Filter size={18} className={`${showFilters ? 'text-[#009639]' : 'text-[#797879]'} transition-colors`} />
            </button>
          )}
        </div>

        {/* Collapsible Filters */}
        {activeTab === "vehicles" && showFilters && (
          <div className="bg-white rounded-2xl p-4 shadow-md border border-gray-100 mb-3 animate-in slide-in-from-top-2 duration-200">
            <div className="space-y-4">
              {/* Results Count and Sponsored Toggle */}
              <div className="flex items-center justify-between pb-3 border-b border-gray-100">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-[#333333] font-medium">
                    {totalCount} listing{totalCount !== 1 ? 's' : ''} found
                    {showSponsoredListing && " (+ 1 sponsored)"}
                  </span>
                </div>
                <button
                  onClick={() => setShowSponsoredListing(!showSponsoredListing)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    showSponsoredListing 
                      ? 'bg-[#009639] text-white' 
                      : 'bg-gray-100 text-[#797879] hover:bg-gray-200'
                  }`}
                >
                  {showSponsoredListing ? "Hide" : "Show"} sponsored
                </button>
              </div>

              {/* Sort Controls */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-[#797879] font-medium mb-2">Sort by</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as "effectiveFrom" | "askingPrice" | "createdAt")}
                    className="w-full px-3 py-2 text-sm bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639]/20 focus:border-[#009639]"
                  >
                    <option value="effectiveFrom">Date Listed</option>
                    <option value="askingPrice">Price</option>
                    <option value="createdAt">Date Created</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-[#797879] font-medium mb-2">Order</label>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as "asc" | "desc")}
                    className="w-full px-3 py-2 text-sm bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639]/20 focus:border-[#009639]"
                  >
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                  </select>
                </div>
              </div>

              {/* Quick Filter Chips */}
              <div>
                <label className="block text-xs text-[#797879] font-medium mb-2">Quick Filters</label>
                <div className="flex flex-wrap gap-2">
                  <button className="px-3 py-1 bg-gray-100 text-[#333333] rounded-full text-xs font-medium hover:bg-gray-200 transition-colors">
                    Available Now
                  </button>
                  <button className="px-3 py-1 bg-gray-100 text-[#333333] rounded-full text-xs font-medium hover:bg-gray-200 transition-colors">
                    Co-ownership
                  </button>
                  <button className="px-3 py-1 bg-gray-100 text-[#333333] rounded-full text-xs font-medium hover:bg-gray-200 transition-colors">
                    Short-term Lease
                  </button>
                  <button className="px-3 py-1 bg-gray-100 text-[#333333] rounded-full text-xs font-medium hover:bg-gray-200 transition-colors">
                    Long-term Lease
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="px-4 flex-shrink-0">
        <div className="flex border-b border-[#f2f2f2] mb-4">
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "vehicles"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("vehicles")}
          >
            Vehicle Listings
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "business"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("business")}
          >
            Business
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "partners"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("partners")}
          >
            Partners
          </button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-4 pb-8">
        {/* Vehicle Listings Tab */}
        {activeTab === "vehicles" && (
          <>
                    {/* Featured Banner */}
            {/* <div className="mb-6">
              <div className="bg-gradient-to-br from-[#009639] via-[#00b347] to-[#007A2F] rounded-lg p-6 border border-[#007A2F] shadow-2xl">
                <div className="flex items-center mb-2">
                  <div className="w-1 h-8 bg-white rounded-full mr-3"></div>
                  <h2 className="text-xl font-bold text-white">Find the perfect ride to lease or co-own</h2>
                </div>
                <p className="text-green-100 mb-4">Browse hundreds of available vehicles in your area.</p>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-black rounded-full mr-2"></div>
                  <span className="text-black text-sm font-medium">Powered by Poolly</span>
                </div>
              </div>
            </div> */}

            {/* Sponsored Listing */}
            {showSponsoredListing && (() => {
              console.log('🌟 Rendering sponsored listing');
              return renderSponsoredListing();
            })()}

            {/* Regular Listings */}
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-[#797879]">Loading listings...</div>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-red-500 text-center mb-4">
                  Error loading listings. Please try again.
                </div>
                <button
                  className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium"
                  onClick={() => mutate()}
                >
                  Retry
                </button>
              </div>
            ) : vehicleListings.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-[#797879] text-center mb-4">
                  No vehicle listings available yet.
                </div>
                <button
                  className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium"
                  onClick={navigateToListVehicle}
                >
                  Be the first to list your vehicle
                </button>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {(() => {
                    console.log('🎨 About to render listings:', {
                      count: vehicleListings.length,
                      ids: vehicleListings.map(l => l.id),
                      showSponsored: showSponsoredListing
                    });
                    return null;
                  })()}
                  {vehicleListings.map((listing) => (
                    <div
                      key={`listing-${listing.id}`}
                      className="ride-card overflow-hidden drop-shadow-md rounded-xl border border-gray-100"
                    >
                      <div className="h-52 bg-[#f2f2f2] relative">
                        <ListingImage
                          media={listing.media}
                          alt={`${listing.vehicle?.model?.make?.name} ${listing.vehicle?.model?.model}`}
                          className="object-cover"
                        />
                        
                        {/* User's Own Listing Indicator */}
                        {currentUserPartyId && listing.partyId === currentUserPartyId && (
                          <div className="absolute top-3 left-3 bg-[#009639] text-white px-3 py-1 rounded-full text-xs font-medium flex items-center shadow-sm">
                            <Crown size={12} className="mr-1" />
                            Your Listing
                          </div>
                        )}
                        
                        {/* Listing Type Badge */}
                        <div className={`absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-medium shadow-sm ${getListingTypeColor(listing.listingType)}`}>
                          <div className="flex items-center">
                            <Tag size={12} className="mr-1" />
                            {formatListingType(listing.listingType)}
                          </div>
                        </div>
                        
                        {/* Ownership Percentage - Only for Co-ownership Sales */}
                        {listing.listingType === "CO_OWNERSHIP_SALE" && (
                          <div className="absolute bottom-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                            {Math.round(listing.fraction)}% Ownership
                          </div>
                        )}
                        
                        {/* Rating 
                        <div className="absolute bottom-3 left-3 bg-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center">
                          <Star size={12} className="text-[#ff5c00] mr-1" />
                          <span>4.5</span>
                          <span className="text-[#797879] ml-1">(12)</span>
                        </div>*/}
                      </div>

                      <div className="p-4">
                        <div className="flex justify-between items-center mb-1">
                          <h3 className="text-[#333333] font-semibold">
                            {listing.vehicle?.model?.make?.name}{" "}
                            {listing.vehicle?.model?.model}
                          </h3>
                          <span className="text-lg font-bold text-[#009639]">
                            R{listing.askingPrice?.toLocaleString() || "0"}
                            {(listing.listingType === "SHORT_TERM_LEASE_OUT" || listing.listingType === "LONG_TERM_LEASE_OUT") && (
                              <span className="text-sm font-medium">/month</span>
                            )}
                          </span>
                        </div>

                        <p className="text-sm text-[#797879] mb-2">
                          {listing.vehicle?.color} •{" "}
                          {listing.vehicle?.manufacturingYear} • {listing.condition}
                        </p>

                        <div className="flex items-center mb-3">
                          <div className="text-xs text-[#797879]">
                            📍{" "}
                            {listing.vehicle?.countryOfRegistration ||
                              "Location not specified"}
                          </div>
                        </div>

                        {/* Interest Expression Section */}
                        <div className="flex items-center justify-between mb-3">
                          <ListingInterestButton
                            listingId={listing.id}
                            listingAuthorPartyId={listing.partyId}
                            currentUserPartyId={currentUserPartyId}
                            initialInterestCount={interestCounts[listing.id] || 0}
                            initialUserHasInterest={
                              userInterestStatus[listing.id] || false
                            }
                            variant="compact"
                          />
                        </div>

                        <button
                          className="ride-primary-btn w-full py-2 text-sm"
                          onClick={() => {
                            // If it's the user's own listing, go to listing management
                            if (currentUserPartyId && listing.partyId === currentUserPartyId) {
                              navigateToListingManagement(listing.id.toString());
                            } else {
                              // Navigate to the read-only listing details screen for others' listings
                              navigateToListingDetails(listing.id.toString());
                            }
                          }}
                        >
                          {currentUserPartyId && listing.partyId === currentUserPartyId 
                            ? "Manage Listing" 
                            : "View Details"
                          }
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {renderPagination()}
              </>
            )}
          </>
        )}

        {/* Business Opportunities Tab */}
        {activeTab === "business" && (
          <div className="space-y-4">
            {businessOpportunities.map((opportunity) => (
              <div
                key={`biz-${opportunity.id}`}
                className="ride-card overflow-hidden drop-shadow-md rounded-xl border border-gray-100"
              >
                <div className="h-32 bg-[#f2f2f2] relative">
                  <Image
                    src={opportunity.image || "/placeholder.svg"}
                    alt={opportunity.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                    {opportunity.category}
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-[#333333] font-semibold">
                      {opportunity.title}
                    </h3>
                  </div>

                  <p className="text-sm text-[#797879] mb-2">
                    {opportunity.description}
                  </p>

                  <div className="flex items-center justify-between mb-3">
                    <div className="text-xs text-[#797879]">
                      Potential Earnings
                    </div>
                    <div className="text-sm font-bold text-[#009639]">
                      {opportunity.earnings}
                    </div>
                  </div>

                  <button
                    className="ride-primary-btn w-full py-2 text-sm"
                    onClick={() =>
                      navigate('business-opportunity', { id: opportunity.id.toString() })
                    }
                  >
                    Apply Now
                  </button>
                </div>
                </div>
            ))}
          </div>
        )}

        {/* Partner Services Tab */}
        {activeTab === "partners" && (
          <div className="space-y-3">
            {partnerServices.map((service) => (
              <div
                key={`partner-${service.id}`}
                className="ride-card p-4 drop-shadow-md rounded-xl border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    {service.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-[#333333] font-medium">
                          {service.title}
                        </h4>
                        <p className="text-xs text-[#797879] mb-1">
                          {service.provider}
                        </p>
                        <p className="text-sm text-[#333333]">
                          {service.description}
                        </p>
                      </div>
              </div>
              </div>
                  <button className="text-[#009639] flex-shrink-0">
                    <ChevronRight size={20} className="text-[#009639]" />
                  </button>
              </div>
              </div>
            ))}
          </div>
        )}
        </div>
      </div>
    </div>
  );
} 