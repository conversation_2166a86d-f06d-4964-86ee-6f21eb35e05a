"use client";

import Image from "next/image";
import type { VehicleMediaRead } from "@/types/vehicles";
import { useAmplifyImage } from "@/hooks/use-amplify-image";

interface VehicleImageProps {
  media?: VehicleMediaRead[];
  alt: string;
  className?: string;
  fallbackSrc?: string;
}

export default function VehicleImage({ 
  media, 
  alt, 
  className = "object-cover",
  fallbackSrc = "/placeholder.svg?height=120&width=200"
}: VehicleImageProps) {
  // Get the first media item (primary image)
  const primaryMediaPath = media && media.length > 0 ? media[0].media_path : undefined;
  
  const { imageUrl, isLoading, error } = useAmplifyImage(
    primaryMediaPath,
    fallbackSrc,
    {
      validateObjectExistence: true,
      expiresIn: 900, // 15 minutes
    }
  );

  if (isLoading) {
    return (
      <div className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}>
        <div className="text-gray-400 text-sm">Loading...</div>
      </div>
    );
  }

  return (
    <Image
      src={imageUrl}
      alt={alt}
      fill
      className={className}
      onError={() => {
        console.error("Vehicle image failed to load:", error);
      }}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
} 