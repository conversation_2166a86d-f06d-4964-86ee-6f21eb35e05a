"use server";

/**
 * SERVER ACTIONS - APPLICATIONS
 *
 * This file contains form-handling server actions for applications.
 * These actions validate input, handle file uploads, and call drizzle actions.
 *
 * Architecture:
 * - actions/* = Server actions for form handling with validation
 * - drizzle-actions/* = Direct database operations
 */

import { revalidatePath } from "next/cache";
import {
  createApplication,
  uploadApplicationDocuments,
  getUserApplications,
  getApplicationById,
  getApplicationsForListing,
  makeApplicationDecision,
  updateDocumentStatus,
  getApplicationStats,
  updateApplicationStatus,
  getApplicationStatusTimeline,
  getCurrentApplicationStatus,
} from "@/drizzle-actions/applications";
import {
  CreateApplicationSchema,
  EhailingApplicationFormSchema,
  RentalApplicationFormSchema,
  FractionalApplicationFormSchema,
  MakeDecisionSchema,
  UpdateDocumentStatusSchema,
  type ApplicationResponse,
  type ApplicationsListResponse,
  type ApplicationStatsResponse,
} from "@/types/applications";

/**
 * Submit an e-hailing application
 */
export async function submitEhailingApplication(
  prevState: any,
  formData: FormData
): Promise<ApplicationResponse> {
  try {
    // Extract form data
    const rawData = {
      listingId: Number(formData.get("listingId")),
      hasEhailingExperience: formData.get("hasEhailingExperience") === "true",
      ehailingCompany: formData.get("ehailingCompany") as string,
      ehailingProfileNumber: formData.get("ehailingProfileNumber") as string,
      ehailingWorkType: formData.get("ehailingWorkType") as string,
      drivingExperienceYears: formData.get("drivingExperienceYears") as string,
      arrangementRequested: formData.get("arrangementRequested") === "true",
    };

    // Validate form data
    const validatedData = EhailingApplicationFormSchema.parse(rawData);

    // Prepare application details
    const applicationData = {
      listingId: validatedData.listingId,
      applicationDetails: {
        hasEhailingExperience: validatedData.hasEhailingExperience,
        ehailingCompany: validatedData.ehailingCompany,
        ehailingProfileNumber: validatedData.ehailingProfileNumber,
        ehailingWorkType: validatedData.ehailingWorkType,
        drivingExperienceYears: validatedData.drivingExperienceYears,
        arrangementRequested: validatedData.arrangementRequested,
      },
    };

    // Create application
    const result = await createApplication(applicationData);

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/home");
      revalidatePath("/profile");
    }

    return result;
  } catch (error) {
    console.error("Error submitting e-hailing application:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to submit application",
    };
  }
}

/**
 * Submit a rental application
 */
export async function submitRentalApplication(
  prevState: any,
  formData: FormData
): Promise<ApplicationResponse> {
  try {
    // Extract form data
    const rawData = {
      listingId: Number(formData.get("listingId")),
      purpose: formData.get("purpose") as string,
    };

    // Validate form data
    const validatedData = RentalApplicationFormSchema.parse(rawData);

    // Prepare application details
    const applicationData = {
      listingId: validatedData.listingId,
      applicationDetails: {
        purpose: validatedData.purpose,
      },
    };

    // Create application
    const result = await createApplication(applicationData);

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/home");
      revalidatePath("/profile");
    }

    return result;
  } catch (error) {
    console.error("Error submitting rental application:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to submit application",
    };
  }
}

/**
 * Submit a fractional ownership application
 */
export async function submitFractionalApplication(
  prevState: any,
  formData: FormData
): Promise<ApplicationResponse> {
  try {
    // Extract form data
    const rawData = {
      listingId: Number(formData.get("listingId")),
    };

    // Validate form data
    const validatedData = FractionalApplicationFormSchema.parse(rawData);

    // Prepare application details
    const applicationData = {
      listingId: validatedData.listingId,
      applicationDetails: {
        // Fractional applications may have minimal details initially
      },
    };

    // Create application
    const result = await createApplication(applicationData);

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/home");
      revalidatePath("/profile");
    }

    return result;
  } catch (error) {
    console.error("Error submitting fractional application:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to submit application",
    };
  }
}

/**
 * Upload documents for an application
 */
export async function uploadApplicationDocumentsAction(
  applicationId: number,
  documents: { documentType: string; documentUrl: string }[]
): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await uploadApplicationDocuments(applicationId, documents);

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/profile");
      revalidatePath(`/application/${applicationId}`);
    }

    return result;
  } catch (error) {
    console.error("Error uploading application documents:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to upload documents",
    };
  }
}

/**
 * Get user's applications
 */
export async function getUserApplicationsAction(): Promise<any> {
  try {
    const result = await getUserApplications();
    return result;
  } catch (error) {
    console.error("Error fetching user applications:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch applications",
    };
  }
}

/**
 * Get a specific application by ID
 */
export async function getApplicationByIdAction(
  applicationId: number
): Promise<any> {
  try {
    const result = await getApplicationById(applicationId);
    return {
      success: result.success,
      applications: result.application ? [result.application] : undefined,
      error: result.error,
    };
  } catch (error) {
    console.error("Error fetching application:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch application",
    };
  }
}

/**
 * Admin: Get applications for a listing
 */
export async function getApplicationsForListingAction(
  listingId: number
): Promise<any> {
  try {
    const result = await getApplicationsForListing(listingId);
    return result;
  } catch (error) {
    console.error("Error fetching applications for listing:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch applications",
    };
  }
}

/**
 * Admin: Make a decision on an application
 */
export async function makeApplicationDecisionAction(
  prevState: any,
  formData: FormData
): Promise<{ success: boolean; error?: string }> {
  try {
    // Extract form data
    const rawData = {
      applicationId: Number(formData.get("applicationId")),
      decision: formData.get("decision") as "approved" | "rejected",
      reason: formData.get("reason") as string,
    };

    // Validate form data
    const validatedData = MakeDecisionSchema.parse(rawData);

    // Make decision
    const result = await makeApplicationDecision(
      validatedData.applicationId,
      validatedData.decision,
      validatedData.reason
    );

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/admin/applications");
      revalidatePath(`/admin/applications/${validatedData.applicationId}`);
    }

    return result;
  } catch (error) {
    console.error("Error making application decision:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to make decision",
    };
  }
}

/**
 * Admin: Update document verification status
 */
export async function updateDocumentStatusAction(
  prevState: any,
  formData: FormData
): Promise<{ success: boolean; error?: string }> {
  try {
    // Extract form data
    const rawData = {
      documentId: Number(formData.get("documentId")),
      status: formData.get("status") as "verified" | "rejected",
    };

    // Validate form data
    const validatedData = UpdateDocumentStatusSchema.parse(rawData);

    // Update document status
    const result = await updateDocumentStatus(
      validatedData.documentId,
      validatedData.status
    );

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/admin/applications");
    }

    return result;
  } catch (error) {
    console.error("Error updating document status:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update document status",
    };
  }
}

/**
 * Admin: Get application statistics
 */
export async function getApplicationStatsAction(): Promise<ApplicationStatsResponse> {
  try {
    const result = await getApplicationStats();
    return result;
  } catch (error) {
    console.error("Error fetching application stats:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch application statistics",
    };
  }
}

/**
 * Update application status
 */
export async function updateApplicationStatusAction(
  prevState: any,
  formData: FormData
): Promise<{ success: boolean; error?: string }> {
  try {
    const applicationId = Number(formData.get("applicationId"));
    const status = formData.get("status") as
      | "pending"
      | "under_review"
      | "approved"
      | "rejected"
      | "withdrawn";
    const reason = formData.get("reason") as string | undefined;

    if (!applicationId || !status) {
      return { success: false, error: "Missing required fields" };
    }

    const result = await updateApplicationStatus(applicationId, status, reason);

    if (result.success) {
      // Revalidate relevant pages
      revalidatePath("/admin/applications");
      revalidatePath("/profile");
      revalidatePath("/home");
    }

    return result;
  } catch (error) {
    console.error("Error updating application status:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update application status",
    };
  }
}

/**
 * Get application status timeline
 */
export async function getApplicationStatusTimelineAction(
  applicationId: number
): Promise<{
  success: boolean;
  timeline?: Array<{
    id: number;
    status: string;
    reason?: string | null;
    timestamp: Date;
    reviewerName?: string | null;
  }>;
  error?: string;
}> {
  try {
    const result = await getApplicationStatusTimeline(applicationId);
    return result;
  } catch (error) {
    console.error("Error fetching application timeline:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch application timeline",
    };
  }
}

/**
 * Get current application status
 */
export async function getCurrentApplicationStatusAction(
  applicationId: number
): Promise<{
  success: boolean;
  status?: "pending" | "under_review" | "approved" | "rejected" | "withdrawn";
  error?: string;
}> {
  try {
    const result = await getCurrentApplicationStatus(applicationId);
    return result;
  } catch (error) {
    console.error("Error fetching application status:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch application status",
    };
  }
}
