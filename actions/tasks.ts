"use server";

import { db } from "@/db";
import { tasks, groupMembershipInvitations, groupMemberships, groupMemberRoles, party, individual, groups } from "@/drizzle/schema";
import { TaskCreate, TaskTypeEnum, TaskStatusEnum, TaskPriorityEnum, GroupInvitationTaskMetadata } from "@/types/tasks";
import { InvitationStatusEnum, GroupRoleEnum } from "@/types/groups";
import { eq, and, or } from "drizzle-orm";

/**
 * Check for pending tasks for a user by email or party ID
 */
export async function checkPendingTasks(email: string, partyId?: number) {
  try {
    const pendingTasks = await db
      .select({
        id: tasks.id,
        type: tasks.type,
        status: tasks.status,
        priority: tasks.priority,
        title: tasks.title,
        description: tasks.description,
        email: tasks.email,
        partyId: tasks.partyId,
        relatedEntityId: tasks.relatedEntityId,
        metadata: tasks.metadata,
        estimatedMinutes: tasks.estimatedMinutes,
        expiresAt: tasks.expiresAt,
        createdAt: tasks.createdAt
      })
      .from(tasks)
      .where(
        and(
          or(
            eq(tasks.email, email),
            partyId ? eq(tasks.partyId, partyId) : undefined
          ),
          eq(tasks.status, TaskStatusEnum.PENDING)
        )
      )
      .orderBy(tasks.priority, tasks.createdAt);

    return {
      success: true,
      tasks: pendingTasks,
      hasGroupInvitations: pendingTasks.some(task => task.type === TaskTypeEnum.GROUP_INVITATION)
    };
  } catch (error) {
    console.error("Error checking pending tasks:", error);
    return {
      success: false,
      error: "Failed to check pending tasks",
      tasks: []
    };
  }
}

/**
 * Create a group invitation task
 */
export async function createGroupInvitationTask(
  email: string,
  invitationId: number,
  groupId: number,
  groupName: string,
  inviterName: string,
  role: GroupRoleEnum,
  partyId?: number
) {
  try {
    const metadata: GroupInvitationTaskMetadata = {
      groupId,
      groupName,
      invitationId,
      inviterName,
      role
    };

    const taskData: TaskCreate = {
      type: TaskTypeEnum.GROUP_INVITATION,
      status: TaskStatusEnum.PENDING,
      priority: TaskPriorityEnum.URGENT,
      title: `Group Invitation: ${groupName}`,
      description: `${inviterName} invited you to join the group "${groupName}" as a ${role.toLowerCase()}.`,
      email,
      partyId,
      relatedEntityId: invitationId,
      metadata,
      estimatedMinutes: 2,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
    };

    const [task] = await db.insert(tasks).values(taskData).returning();

    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("Error creating group invitation task:", error);
    return {
      success: false,
      error: "Failed to create group invitation task"
    };
  }
}

/**
 * Accept a group invitation task
 */
export async function acceptGroupInvitation(taskId: number, partyId: number) {
  try {
    // Get the task
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task || task.type !== TaskTypeEnum.GROUP_INVITATION) {
      return {
        success: false,
        error: "Task not found or invalid type"
      };
    }

    const metadata = task.metadata as GroupInvitationTaskMetadata;
    const invitationId = task.relatedEntityId;

    if (!invitationId || !metadata) {
      return {
        success: false,
        error: "Invalid task data"
      };
    }

    // Get the invitation
    const [invitation] = await db
      .select()
      .from(groupMembershipInvitations)
      .where(eq(groupMembershipInvitations.id, invitationId));

    if (!invitation || invitation.status !== InvitationStatusEnum.SENT) {
      return {
        success: false,
        error: "Invitation not found or already processed"
      };
    }

    // Start transaction to accept invitation
    const result = await db.transaction(async (tx) => {
      // Update invitation status
      await tx
        .update(groupMembershipInvitations)
        .set({
          status: InvitationStatusEnum.ACCEPTED,
          acceptedAt: new Date().toISOString(),
          acceptedBy: partyId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(groupMembershipInvitations.id, invitationId));

      // Add to group memberships
      await tx.insert(groupMemberships).values({
        groupId: metadata.groupId,
        partyId: partyId,
        effectiveFrom: new Date().toISOString()
      });

      // Add group member role
      await tx.insert(groupMemberRoles).values({
        groupId: metadata.groupId,
        partyId: partyId,
        role: invitation.role,
        effectiveFrom: new Date().toISOString()
      });

      // Complete the task
      await tx
        .update(tasks)
        .set({
          status: TaskStatusEnum.COMPLETED,
          completedAt: new Date().toISOString(),
          completedBy: partyId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(tasks.id, taskId));

      return { success: true };
    });

    return result;
  } catch (error) {
    console.error("Error accepting group invitation:", error);
    return {
      success: false,
      error: "Failed to accept group invitation"
    };
  }
}

/**
 * Decline a group invitation task
 */
export async function declineGroupInvitation(taskId: number, partyId: number) {
  try {
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task || task.type !== TaskTypeEnum.GROUP_INVITATION) {
      return {
        success: false,
        error: "Task not found or invalid type"
      };
    }

    const invitationId = task.relatedEntityId;
    if (!invitationId) {
      return {
        success: false,
        error: "Invalid task data"
      };
    }

    // Start transaction to decline invitation
    const result = await db.transaction(async (tx) => {
      // Update invitation status
      await tx
        .update(groupMembershipInvitations)
        .set({
          status: InvitationStatusEnum.DECLINED,
          updatedAt: new Date().toISOString()
        })
        .where(eq(groupMembershipInvitations.id, invitationId));

      // Complete the task
      await tx
        .update(tasks)
        .set({
          status: TaskStatusEnum.COMPLETED,
          completedAt: new Date().toISOString(),
          completedBy: partyId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(tasks.id, taskId));

      return { success: true };
    });

    return result;
  } catch (error) {
    console.error("Error declining group invitation:", error);
    return {
      success: false,
      error: "Failed to decline group invitation"
    };
  }
}

/**
 * Get task details by ID
 */
export async function getTaskById(taskId: number) {
  try {
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task) {
      return {
        success: false,
        error: "Task not found"
      };
    }

    // If it's a group invitation task, get additional details
    if (task.type === TaskTypeEnum.GROUP_INVITATION && task.relatedEntityId) {
      const invitationDetails = await db
        .select({
          invitation: groupMembershipInvitations,
          groupName: groups.name,
          inviterFirstName: individual.firstName,
          inviterLastName: individual.lastName
        })
        .from(groupMembershipInvitations)
        .leftJoin(groups, eq(groupMembershipInvitations.groupId, groups.id))
        .leftJoin(party, eq(groupMembershipInvitations.invitedBy, party.id))
        .leftJoin(individual, eq(party.id, individual.partyId))
        .where(eq(groupMembershipInvitations.id, task.relatedEntityId));

      if (invitationDetails.length > 0) {
        const details = invitationDetails[0];
        return {
          success: true,
          task: {
            ...task,
            invitationDetails: {
              ...details.invitation,
              groupName: details.groupName,
              inviterName: details.inviterFirstName && details.inviterLastName 
                ? `${details.inviterFirstName} ${details.inviterLastName}`
                : "Unknown"
            }
          }
        };
      }
    }

    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("Error getting task by ID:", error);
    return {
      success: false,
      error: "Failed to get task details"
    };
  }
} 