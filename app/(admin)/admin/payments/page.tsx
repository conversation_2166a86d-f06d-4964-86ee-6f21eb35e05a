"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  DollarSign,
  User,
  Car,
  Calendar,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  Settings,
  Download,
  Send,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import PaymentRecordDialog from "./components/PaymentRecordDialog";
import DriverPaymentHistoryDialog from "./components/DriverPaymentHistoryDialog";
import type { PaymentRecord } from "../types/payments";
import type { BasicAssignment } from "../types/assignments";

export default function PaymentsPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [isDriverHistoryDialogOpen, setIsDriverHistoryDialogOpen] =
    useState(false);
  const [selectedDriverForHistory, setSelectedDriverForHistory] = useState<
    string | null
  >(null);

  // Mock assignments data for payment dialog
  const [assignments] = useState<BasicAssignment[]>([
    {
      id: "assign-001",
      driverName: "John Doe",
      vehicleName: "Suzuki Dzire 2023",
      vehicleRegistration: "CA 123-456",
      weeklyRate: 2700,
      outstandingBalance: 0,
    },
    {
      id: "assign-002",
      driverName: "Sarah Smith",
      vehicleName: "Toyota Corolla Quest 2022",
      vehicleRegistration: "GP 345-678",
      weeklyRate: 2900,
      outstandingBalance: 3150,
    },
    {
      id: "assign-003",
      driverName: "Mike Johnson",
      vehicleName: "Nissan Almera 2023",
      vehicleRegistration: "KZN 789-012",
      weeklyRate: 2800,
      outstandingBalance: 7800,
    },
  ]);

  // Mock data - replace with actual API call
  const [payments] = useState<PaymentRecord[]>([
    {
      id: "pay-001",
      assignmentId: "assign-001",
      driverName: "John Doe",
      vehicleName: "Suzuki Dzire 2023",
      vehicleRegistration: "CA 123-456",
      paymentType: "weekly_lease",
      amount: 2700,
      dueDate: "2024-01-29",
      paidDate: "2024-01-28",
      status: "paid",
      paymentMethod: "bank_transfer",
      reference: "REF123456",
    },
    {
      id: "pay-002",
      assignmentId: "assign-001",
      driverName: "John Doe",
      vehicleName: "Suzuki Dzire 2023",
      vehicleRegistration: "CA 123-456",
      paymentType: "weekly_lease",
      amount: 2700,
      dueDate: "2024-02-05",
      status: "pending",
    },
    {
      id: "pay-003",
      assignmentId: "assign-002",
      driverName: "Sarah Smith",
      vehicleName: "Toyota Corolla Quest 2022",
      vehicleRegistration: "GP 345-678",
      paymentType: "initiation_fee",
      amount: 3000,
      dueDate: "2024-01-25",
      status: "overdue",
      lateFee: 150,
      notes: "Partial payment of R5000 received",
    },
    {
      id: "pay-004",
      assignmentId: "assign-003",
      driverName: "Mike Johnson",
      vehicleName: "Nissan Almera 2023",
      vehicleRegistration: "KZN 789-012",
      paymentType: "initiation_fee",
      amount: 7800,
      dueDate: "2024-02-01",
      status: "pending",
    },
    {
      id: "pay-005",
      assignmentId: "assign-002",
      driverName: "Sarah Smith",
      vehicleName: "Toyota Corolla Quest 2022",
      vehicleRegistration: "GP 345-678",
      paymentType: "late_fee",
      amount: 150,
      dueDate: "2024-02-01",
      status: "overdue",
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "partial":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <CheckCircle size={12} className="mr-1" />;
      case "pending":
        return <Clock size={12} className="mr-1" />;
      case "overdue":
        return <AlertTriangle size={12} className="mr-1" />;
      case "partial":
        return <XCircle size={12} className="mr-1" />;
      default:
        return <Clock size={12} className="mr-1" />;
    }
  };

  const getPaymentTypeColor = (type: string) => {
    switch (type) {
      case "weekly_lease":
        return "bg-blue-100 text-blue-800";
      case "initiation_fee":
        return "bg-purple-100 text-purple-800";
      case "late_fee":
        return "bg-red-100 text-red-800";
      case "maintenance":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredPayments = payments.filter((payment) => {
    const matchesSearch =
      payment.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.vehicleRegistration
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      payment.reference?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" || payment.status === filterStatus;
    const matchesType =
      filterType === "all" || payment.paymentType === filterType;
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "pending" && payment.status === "pending") ||
      (activeTab === "overdue" && payment.status === "overdue") ||
      (activeTab === "paid" && payment.status === "paid");

    return matchesSearch && matchesStatus && matchesType && matchesTab;
  });

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return payments.length;
      case "pending":
        return payments.filter((p) => p.status === "pending").length;
      case "overdue":
        return payments.filter((p) => p.status === "overdue").length;
      case "paid":
        return payments.filter((p) => p.status === "paid").length;
      default:
        return 0;
    }
  };

  const getTotalAmount = (status?: string) => {
    const filtered = status
      ? payments.filter((p) => p.status === status)
      : payments;
    return filtered.reduce((sum, p) => sum + p.amount + (p.lateFee || 0), 0);
  };

  const isOverdue = (dueDate: string, status: string) => {
    return status !== "paid" && new Date(dueDate) < new Date();
  };

  const handleRecordPayment = () => {
    setIsPaymentDialogOpen(true);
  };

  const handlePaymentSubmit = (paymentData: any) => {
    // TODO: Implement API call to record payment
    console.log("Payment data:", paymentData);
    // Close dialog
    handleClosePaymentDialog();
  };

  const handleClosePaymentDialog = () => {
    setIsPaymentDialogOpen(false);
  };

  const handleViewPaymentDetails = (payment: PaymentRecord) => {
    // TODO: Implement view payment details
    console.log("View payment details:", payment);
  };

  const handleMarkAsPaid = (payment: PaymentRecord) => {
    // TODO: Implement mark as paid
    console.log("Mark as paid:", payment);
  };

  const handleRecordPartialPayment = (payment: PaymentRecord) => {
    // TODO: Implement record partial payment
    console.log("Record partial payment:", payment);
  };

  const handleViewDriverHistory = (driverName: string) => {
    setSelectedDriverForHistory(driverName);
    setIsDriverHistoryDialogOpen(true);
  };

  const handleCloseDriverHistoryDialog = () => {
    setIsDriverHistoryDialogOpen(false);
    setSelectedDriverForHistory(null);
  };

  // Mock driver data for history dialog
  const getDriverInfo = (driverName: string) => {
    const driverData = {
      "John Doe": {
        id: "drv-001",
        name: "John Doe",
        email: "<EMAIL>",
        phone: "+27 82 123 4567",
        vehicleName: "Suzuki Dzire 2023",
        vehicleRegistration: "CA 123-456",
        assignmentDate: "2024-01-15",
        weeklyRate: 2700,
        totalPaid: 21600,
        outstandingBalance: 0,
        paymentScore: 95,
        onTimePayments: 8,
        totalPayments: 8,
      },
      "Sarah Smith": {
        id: "drv-002",
        name: "Sarah Smith",
        email: "<EMAIL>",
        phone: "+27 83 234 5678",
        vehicleName: "Toyota Corolla Quest 2022",
        vehicleRegistration: "GP 345-678",
        assignmentDate: "2024-01-20",
        weeklyRate: 2600,
        totalPaid: 15600,
        outstandingBalance: 3150,
        paymentScore: 72,
        onTimePayments: 4,
        totalPayments: 6,
      },
      "Mike Johnson": {
        id: "drv-003",
        name: "Mike Johnson",
        email: "<EMAIL>",
        phone: "+27 84 345 6789",
        vehicleName: "Nissan Almera 2023",
        vehicleRegistration: "KZN 789-012",
        assignmentDate: "2024-01-25",
        weeklyRate: 2800,
        totalPaid: 11200,
        outstandingBalance: 0,
        paymentScore: 100,
        onTimePayments: 4,
        totalPayments: 4,
      },
    };
    return driverData[driverName as keyof typeof driverData] || null;
  };

  const getDriverPaymentHistory = (driverName: string) => {
    // Return filtered payment history for the specific driver
    return payments.filter((payment) => payment.driverName === driverName);
  };

  const handleSendReminder = (payment: PaymentRecord) => {
    // TODO: Implement send reminder
    console.log("Send reminder:", payment);
  };

  const handleGenerateInvoice = (payment: PaymentRecord) => {
    // TODO: Implement generate invoice
    console.log("Generate invoice:", payment);
  };

  const handleApplyLateFee = (payment: PaymentRecord) => {
    // TODO: Implement apply late fee
    console.log("Apply late fee:", payment);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Payment Tracking</h1>
          <p className="text-gray-600 mt-1">
            Monitor lease payments, fees, and financial obligations
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Report
          </Button>
          <Button
            onClick={handleRecordPayment}
            className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Record Payment
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Outstanding</p>
                <p className="text-2xl font-bold mt-1">
                  R{getTotalAmount("overdue").toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <TrendingDown className="text-red-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Payments</p>
                <p className="text-2xl font-bold mt-1">
                  R{getTotalAmount("pending").toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Paid This Month</p>
                <p className="text-2xl font-bold mt-1">
                  R{getTotalAmount("paid").toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Late Fees</p>
                <p className="text-2xl font-bold mt-1">
                  R
                  {payments
                    .reduce((sum, p) => sum + (p.lateFee || 0), 0)
                    .toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                <AlertTriangle className="text-orange-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All Payments ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger
                  value="pending"
                  className="flex items-center gap-2"
                >
                  <Clock size={16} />
                  Pending ({getTabCount("pending")})
                </TabsTrigger>
                <TabsTrigger
                  value="overdue"
                  className="flex items-center gap-2"
                >
                  <AlertTriangle size={16} />
                  Overdue ({getTabCount("overdue")})
                </TabsTrigger>
                <TabsTrigger value="paid" className="flex items-center gap-2">
                  <CheckCircle size={16} />
                  Paid ({getTabCount("paid")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search payments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Types</option>
                <option value="weekly_lease">Weekly Lease</option>
                <option value="initiation_fee">Initiation Fee</option>
                <option value="late_fee">Late Fee</option>
                <option value="maintenance">Maintenance</option>
              </select>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="paid">Paid</option>
                <option value="pending">Pending</option>
                <option value="overdue">Overdue</option>
                <option value="partial">Partial</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Driver & Vehicle</TableHead>
                  <TableHead>Payment Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment Details</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPayments.map((payment) => (
                  <TableRow
                    key={payment.id}
                    className={
                      isOverdue(payment.dueDate, payment.status)
                        ? "bg-red-50"
                        : ""
                    }
                  >
                    <TableCell>
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          <User size={14} className="text-gray-400" />
                          <button
                            onClick={() =>
                              handleViewDriverHistory(payment.driverName)
                            }
                            className="text-[#009639] hover:text-[#007A2F] hover:underline"
                          >
                            {payment.driverName}
                          </button>
                        </div>
                        <div className="text-sm text-gray-500 flex items-center gap-2 mt-1">
                          <Car size={14} className="text-gray-400" />
                          {payment.vehicleName}
                        </div>
                        <div className="text-xs text-gray-400">
                          {payment.vehicleRegistration}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getPaymentTypeColor(payment.paymentType)}
                      >
                        {payment.paymentType
                          .replace("_", " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          R{payment.amount.toLocaleString()}
                        </div>
                        {payment.lateFee && (
                          <div className="text-sm text-red-600">
                            + R{payment.lateFee} late fee
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {new Date(payment.dueDate).toLocaleDateString()}
                      </div>
                      {isOverdue(payment.dueDate, payment.status) && (
                        <div className="text-xs text-red-600 mt-1">
                          {Math.ceil(
                            (new Date().getTime() -
                              new Date(payment.dueDate).getTime()) /
                              (1000 * 60 * 60 * 24)
                          )}{" "}
                          days overdue
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getStatusColor(payment.status)}
                      >
                        {getStatusIcon(payment.status)}
                        {payment.status.charAt(0).toUpperCase() +
                          payment.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {payment.paidDate ? (
                        <div>
                          <div className="text-sm">
                            Paid:{" "}
                            {new Date(payment.paidDate).toLocaleDateString()}
                          </div>
                          {payment.paymentMethod && (
                            <div className="text-xs text-gray-500 capitalize">
                              {payment.paymentMethod.replace("_", " ")}
                            </div>
                          )}
                          {payment.reference && (
                            <div className="text-xs text-gray-400">
                              Ref: {payment.reference}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Not paid</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {payment.notes ? (
                        <div className="text-sm text-gray-600 max-w-[200px] truncate">
                          {payment.notes}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Settings size={16} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewPaymentDetails(payment)}
                          >
                            <Eye size={14} className="mr-2" />
                            View Details
                          </DropdownMenuItem>
                          {payment.status !== "paid" && (
                            <>
                              <DropdownMenuItem
                                className="text-green-600"
                                onClick={() => handleMarkAsPaid(payment)}
                              >
                                <CheckCircle size={14} className="mr-2" />
                                Mark as Paid
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-blue-600"
                                onClick={() =>
                                  handleRecordPartialPayment(payment)
                                }
                              >
                                <Edit size={14} className="mr-2" />
                                Record Partial Payment
                              </DropdownMenuItem>
                            </>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleSendReminder(payment)}
                          >
                            <Send size={14} className="mr-2" />
                            Send Reminder
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleGenerateInvoice(payment)}
                          >
                            <Download size={14} className="mr-2" />
                            Generate Invoice
                          </DropdownMenuItem>
                          {payment.status === "overdue" && (
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleApplyLateFee(payment)}
                            >
                              <AlertTriangle size={14} className="mr-2" />
                              Apply Late Fee
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Payment Record Dialog */}
      <PaymentRecordDialog
        isOpen={isPaymentDialogOpen}
        onClose={handleClosePaymentDialog}
        onConfirm={handlePaymentSubmit}
        assignments={assignments}
      />

      {/* Driver Payment History Dialog */}
      <DriverPaymentHistoryDialog
        isOpen={isDriverHistoryDialogOpen}
        onClose={handleCloseDriverHistoryDialog}
        driverInfo={
          selectedDriverForHistory
            ? getDriverInfo(selectedDriverForHistory)
            : null
        }
        paymentHistory={
          selectedDriverForHistory
            ? getDriverPaymentHistory(selectedDriverForHistory)
            : []
        }
      />
    </div>
  );
}
