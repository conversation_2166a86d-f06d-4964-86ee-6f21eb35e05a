"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  Car,
  FileText,
  Edit,
  Eye,
  Settings,
  CheckCircle,
  Clock,
  AlertTriangle,
  Wrench,
  User,
  Calendar,
  MapPin,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import VehicleInventoryFormDialog from "./components/VehicleInventoryFormDialog";
import { forceBodyStyleCleanup } from "../hooks/useBodyStyleCleanup";
import type { InventoryVehicle, VehicleDocument } from "../types/inventory";

export default function InventoryPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<InventoryVehicle | null>(
    null
  );
  const [dialogMode, setDialogMode] = useState<"add" | "edit" | "view">("add");

  // Mock catalog data for vehicle selection
  const [catalogItems] = useState([
    {
      id: "cat-001",
      make: "Suzuki",
      model: "Dzire",
      year: 2023,
      category: "sedan" as "sedan" | "suv" | "hatchback" | "bakkie",
      weeklyRate: 2700,
      initiationFee: 7500,
      features: ["Air Conditioning", "Power Steering", "Electric Windows"],
      suitableFor: ["Uber", "Bolt", "Taxify"],
      isActive: true,
      createdAt: "2024-01-15",
      updatedAt: "2024-01-20",
    },
    {
      id: "cat-002",
      make: "Toyota",
      model: "Corolla Quest",
      year: 2022,
      category: "sedan" as "sedan" | "suv" | "hatchback" | "bakkie",
      weeklyRate: 2900,
      initiationFee: 8000,
      features: ["Air Conditioning", "Power Steering", "ABS", "Airbags"],
      suitableFor: ["Uber", "Bolt", "InDriver"],
      isActive: true,
      createdAt: "2024-01-10",
      updatedAt: "2024-01-18",
    },
    {
      id: "cat-003",
      make: "Nissan",
      model: "Almera",
      year: 2023,
      category: "sedan" as "sedan" | "suv" | "hatchback" | "bakkie",
      weeklyRate: 2800,
      initiationFee: 7800,
      features: ["Air Conditioning", "Power Steering", "Electric Windows"],
      suitableFor: ["Uber", "Bolt", "Taxify"],
      isActive: false,
      createdAt: "2024-01-05",
      updatedAt: "2024-01-12",
    },
  ]);

  // Mock data - replace with actual API call
  const [vehicles] = useState<InventoryVehicle[]>([
    {
      id: "inv-001",
      catalogId: "cat-001",
      make: "Suzuki",
      model: "Dzire",
      year: 2023,
      color: "White",
      vinNumber: "MHRFK16S7N7123456",
      registrationNumber: "CA 123-456",
      mileage: 15000,
      condition: "new",
      status: "assigned",
      location: "Cape Town",
      assignedDriver: {
        id: "drv-001",
        name: "John Doe",
        email: "<EMAIL>",
        assignedDate: "2024-01-15",
      },
      vehicleDocuments: [
        {
          id: "doc-001",
          type: "registration",
          name: "Vehicle Registration Document",
          filePath: "/documents/registration-001.pdf",
          uploadDate: "2024-01-01T00:00:00Z",
          expiryDate: "2025-01-01",
          status: "valid",
        },
        {
          id: "doc-002",
          type: "insurance",
          name: "Insurance Certificate",
          filePath: "/documents/insurance-001.pdf",
          uploadDate: "2024-01-01T00:00:00Z",
          expiryDate: "2024-12-31",
          status: "expiring_soon",
        },
      ],
      lastInspection: "2024-01-10",
      nextService: "2024-04-10",
      createdAt: "2024-01-01",
    },
    {
      id: "inv-002",
      catalogId: "cat-001",
      make: "Suzuki",
      model: "Dzire",
      year: 2023,
      color: "Silver",
      vinNumber: "MHRFK16S7N7123457",
      registrationNumber: "CA 789-012",
      mileage: 100,
      condition: "new",
      status: "available",
      location: "Johannesburg",
      vehicleDocuments: [],
      lastInspection: "2024-01-20",
      nextService: "2024-05-20",
      createdAt: "2024-01-05",
    },
    {
      id: "inv-003",
      catalogId: "cat-002",
      make: "Toyota",
      model: "Corolla Quest",
      year: 2022,
      color: "Blue",
      vinNumber: "JTDBL40E0X9123456",
      registrationNumber: "GP 345-678",
      mileage: 100,
      condition: "used",
      status: "maintenance",
      location: "Durban",
      vehicleDocuments: [],
      lastInspection: "2023-12-15",
      nextService: "2024-03-15",
      createdAt: "2023-12-01",
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800 border-green-200";
      case "assigned":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "inspection":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "available":
        return <CheckCircle size={12} className="mr-1" />;
      case "assigned":
        return <User size={12} className="mr-1" />;
      case "maintenance":
        return <Wrench size={12} className="mr-1" />;
      case "inspection":
        return <Eye size={12} className="mr-1" />;
      default:
        return <Clock size={12} className="mr-1" />;
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "excellent":
        return "bg-green-100 text-green-800";
      case "good":
        return "bg-blue-100 text-blue-800";
      case "fair":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredVehicles = vehicles.filter((vehicle) => {
    const matchesSearch =
      vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.registrationNumber
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      vehicle.assignedDriver?.name
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" || vehicle.status === filterStatus;
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "available" && vehicle.status === "available") ||
      (activeTab === "assigned" && vehicle.status === "assigned") ||
      (activeTab === "maintenance" &&
        (vehicle.status === "maintenance" || vehicle.status === "inspection"));

    return matchesSearch && matchesStatus && matchesTab;
  });

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return vehicles.length;
      case "available":
        return vehicles.filter((v) => v.status === "available").length;
      case "assigned":
        return vehicles.filter((v) => v.status === "assigned").length;
      case "maintenance":
        return vehicles.filter(
          (v) => v.status === "maintenance" || v.status === "inspection"
        ).length;
      default:
        return 0;
    }
  };

  const getDocumentStatus = (vehicleDocuments?: VehicleDocument[]) => {
    if (!vehicleDocuments) return { completed: 0, total: 0 };
    const total = vehicleDocuments.length;
    const completed = vehicleDocuments.filter(
      (doc) => doc.status === "valid"
    ).length;
    return { completed, total };
  };

  const handleAddVehicle = () => {
    setEditingVehicle(null);
    setDialogMode("add");
    setIsFormDialogOpen(true);
  };

  const handleEditVehicle = (vehicle: InventoryVehicle) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setEditingVehicle(vehicle);
      setDialogMode("edit");
      setIsFormDialogOpen(true);
    }, 100);
  };

  const handleViewVehicle = (vehicle: InventoryVehicle) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setEditingVehicle(vehicle);
      setDialogMode("view");
      setIsFormDialogOpen(true);
    }, 100);
  };

  const handleFormSubmit = (vehicleData: InventoryVehicle) => {
    // TODO: Implement API call to save/update vehicle
    console.log("Vehicle data:", vehicleData);
    // Close dialog and reset state
    handleCloseDialog();
  };

  const handleCloseDialog = () => {
    setIsFormDialogOpen(false);
    setEditingVehicle(null);
    setDialogMode("add");
    // Force cleanup of body styles to prevent freezing
    setTimeout(() => {
      forceBodyStyleCleanup();
    }, 100);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Vehicle Inventory</h1>
        <Button
          onClick={handleAddVehicle}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Vehicle to Inventory
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Vehicles</p>
                <p className="text-2xl font-bold mt-1">{vehicles.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Car className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Available</p>
                <p className="text-2xl font-bold mt-1">
                  {vehicles.filter((v) => v.status === "available").length}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Assigned</p>
                <p className="text-2xl font-bold mt-1">
                  {vehicles.filter((v) => v.status === "assigned").length}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">In Maintenance</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    vehicles.filter(
                      (v) =>
                        v.status === "maintenance" || v.status === "inspection"
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Wrench className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger
                  value="available"
                  className="flex items-center gap-2"
                >
                  <CheckCircle size={16} />
                  Available ({getTabCount("available")})
                </TabsTrigger>
                <TabsTrigger
                  value="assigned"
                  className="flex items-center gap-2"
                >
                  <User size={16} />
                  Assigned ({getTabCount("assigned")})
                </TabsTrigger>
                <TabsTrigger
                  value="maintenance"
                  className="flex items-center gap-2"
                >
                  <Wrench size={16} />
                  Maintenance ({getTabCount("maintenance")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search vehicles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by vehicle status"
              >
                <option value="all">All Status</option>
                <option value="available">Available</option>
                <option value="assigned">Assigned</option>
                <option value="maintenance">Maintenance</option>
                <option value="inspection">Inspection</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vehicle Details</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Assigned Driver</TableHead>
                  <TableHead>Assignment Date</TableHead>
                  <TableHead>Documents</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVehicles.map((vehicle) => {
                  const docStatus = getDocumentStatus(vehicle.vehicleDocuments);
                  return (
                    <TableRow key={vehicle.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {vehicle.make} {vehicle.model}
                          </div>
                          <div className="text-sm text-gray-500">
                            {vehicle.year} • {vehicle.registrationNumber}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={getStatusColor(vehicle.status)}
                        >
                          {getStatusIcon(vehicle.status)}
                          {vehicle.status.charAt(0).toUpperCase() +
                            vehicle.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {vehicle.assignedDriver ? (
                          <div>
                            <div className="font-medium">
                              {vehicle.assignedDriver.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {vehicle.assignedDriver.email}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400">Not assigned</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {vehicle.assignedDriver ? (
                          <div className="text-sm">
                            {new Date(
                              vehicle.assignedDriver.assignedDate
                            ).toLocaleDateString("en-GB")}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">
                            {docStatus.completed}/{docStatus.total}
                          </span>
                          {docStatus.completed === docStatus.total ? (
                            <CheckCircle size={16} className="text-green-500" />
                          ) : (
                            <AlertTriangle
                              size={16}
                              className="text-yellow-500"
                            />
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleViewVehicle(vehicle);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Eye size={16} className="text-gray-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleEditVehicle(vehicle);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Edit size={16} className="text-gray-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Inventory Form Dialog */}
      <VehicleInventoryFormDialog
        isOpen={isFormDialogOpen}
        onClose={handleCloseDialog}
        onConfirm={handleFormSubmit}
        editingVehicle={editingVehicle}
        mode={dialogMode}
        catalogItems={catalogItems}
      />
    </div>
  );
}
