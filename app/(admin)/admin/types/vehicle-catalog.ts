import type { InventoryVehicle } from "./inventory";

export interface VehicleCatalogFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (vehicleData: VehicleCatalogItem) => void;
  editingVehicle?: any | null; // Accept any vehicle data structure
  mode?: "add" | "edit" | "view";
}

export interface VehicleCatalogItem {
  id: number; // Changed to number to match database
  make: string;
  model: string;
  year: number;
  variant?: string;
  category: string; // Made more flexible to match database
  fuelType: string; // Added to match database
  ehailingEligible: boolean; // Added to match database
  estimatedPrice?: number; // Added to match database
  weeklyFeeTarget?: number; // Renamed from weeklyRate to match database
  description?: string;
  specifications?: string; // Added to match database
  createdAt?: string;
  updatedAt?: string;
  features: string[];
  platforms: string[]; // Renamed from suitableFor to match database
  images: Array<{
    id: number;
    imageUrl: string;
    isPrimary: boolean;
  }>; // Added to match database

  // Legacy fields for backward compatibility
  weeklyRate?: number; // Computed from weeklyFeeTarget
  initiationFee?: number; // Can be computed from specifications
  suitableFor?: string[]; // Computed from platforms
  isActive?: boolean; // Can be computed from ehailingEligible
  imageUrl?: string; // Computed from primary image
}
