import type { InventoryVehicle } from "./inventory";

export interface VehicleCatalogFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (vehicleData: VehicleCatalogItem) => void;
  editingVehicle?: any | null; // Accept any vehicle data structure
  mode?: "add" | "edit" | "view";
}

export interface VehicleCatalogItem {
  id: string;
  make: string;
  model: string;
  year: number;
  category: "sedan" | "suv" | "hatchback" | "bakkie";
  weeklyRate: number;
  initiationFee: number;
  features: string[];
  suitableFor: string[];
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  description?: string;
  imageUrl?: string;
}
