"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Users,
  BarChart,
  FileText,
  Settings,
  HelpCircle,
  Search,
  Bell,
  MessageSquare,
  Activity,
  History,
  CheckSquare,
  Handshake,
  LineChart,
  Car,
  Wrench,
  ClipboardCheck,
  ArrowLeftRight,
  DollarSign,
  BookOpen,
  Shield,
  TrendingUp,
  Building,
  Gavel,
  FileSearch,
  Share2,
  Globe,
  Database,
} from "lucide-react";
import Image from "next/image";

// Define navigation items with their implementability status
const navigationItems = {
  main: [
    {
      href: "/admin/dashboard",
      icon: LayoutDashboard,
      label: "Dashboard",
      status: "implemented",
    },
    {
      href: "/admin/users",
      icon: Users,
      label: "Users",
      status: "implemented",
    },
    {
      href: "/admin/groups",
      icon: Users,
      label: "Groups",
      status: "implemented",
    },
    {
      href: "/admin/bookings",
      icon: FileText,
      label: "Bookings",
      status: "implemented",
    },
  ],
  management: [
    {
      href: "/admin/vehicles",
      icon: Car,
      label: "Vehicles",
      status: "implemented",
    },
    {
      href: "/admin/vehicle-catalog",
      icon: BookOpen,
      label: "Vehicle Catalog",
      status: "implemented",
    },
    {
      href: "/admin/inventory",
      icon: ClipboardCheck,
      label: "Inventory",
      status: "implemented",
    },
    {
      href: "/admin/assignments",
      icon: ArrowLeftRight,
      label: "Vehicle Assignments",
      status: "implemented",
    },
    {
      href: "/admin/maintenance",
      icon: Wrench,
      label: "Maintenance",
      status: "implemented",
    },
    {
      href: "/admin/compliance",
      icon: ClipboardCheck,
      label: "Compliance",
      status: "implemented",
    },
    {
      href: "/admin/handovers",
      icon: ArrowLeftRight,
      label: "Handovers",
      status: "partial",
    },
    {
      href: "/admin/legal-entities",
      icon: Building,
      label: "Legal Entities",
      status: "partial",
    },
    {
      href: "/admin/fractions",
      icon: Share2,
      label: "Fractions",
      status: "partial",
    },
  ],
  monitoring: [
    {
      href: "/admin/applications",
      icon: FileText,
      label: "Applications",
      status: "implemented",
    },
    {
      href: "/admin/website-leads",
      icon: Globe,
      label: "Website Leads",
      status: "implemented",
    },
    {
      href: "/admin/disputes",
      icon: Gavel,
      label: "Disputes",
      status: "implemented",
    },
    {
      href: "/admin/payments",
      icon: DollarSign,
      label: "Payment Tracking",
      status: "implemented",
    },
    {
      href: "/admin/performance",
      icon: TrendingUp,
      label: "Driver Performance",
      status: "implemented",
    },
    {
      href: "/admin/partners",
      icon: Handshake,
      label: "Partners",
      status: "partial",
    },
    {
      href: "/admin/listings",
      icon: FileText,
      label: "Vehicle Listings",
      status: "implemented",
    },
    {
      href: "/admin/transactions",
      icon: DollarSign,
      label: "Transactions",
      status: "not-implementable",
    },
    {
      href: "/admin/approvals",
      icon: CheckSquare,
      label: "Approvals",
      status: "not-implementable",
    },
    {
      href: "/admin/income-tracking",
      icon: TrendingUp,
      label: "Income Tracking",
      status: "not-implementable",
    },
  ],
  insights: [
    {
      href: "/admin/analytics",
      icon: LineChart,
      label: "Analytics",
      status: "partial",
    },
    {
      href: "/admin/system-logs",
      icon: Activity,
      label: "System Logs",
      status: "not-implementable",
    },
    {
      href: "/admin/audit-trails",
      icon: History,
      label: "Audit Trails",
      status: "not-implementable",
    },
  ],
  system: [
    { href: "/admin/data-management", icon: Database, label: "Data Management", status: "implemented" },
    {
      href: "/admin/settings",
      icon: Settings,
      label: "Settings",
      status: "not-implementable",
    },
    {
      href: "/admin/content",
      icon: BookOpen,
      label: "Content",
      status: "not-implementable",
    },
    {
      href: "/admin/help",
      icon: HelpCircle,
      label: "Help",
      status: "not-implementable",
    },
  ],
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path;
  };

  const renderNavItem = (item: any) => {
    const { href, icon: Icon, label, status } = item;
    const isCurrentlyActive = isActive(href);

    if (status === "not-implementable") {
      return (
        <div
          key={href}
          className="flex items-center px-4 py-2 rounded-md text-gray-400 opacity-60 cursor-not-allowed relative"
        >
          <Icon size={18} className="mr-3" />
          <span className="flex-1">{label}</span>
          <span className="text-xs bg-gray-500 text-white px-2 py-1 rounded-full ml-2">
            Soon
          </span>
        </div>
      );
    }

    if (status === "partial") {
      return (
        <Link
          key={href}
          href={href}
          className={`flex items-center px-4 py-2 rounded-md relative ${
            isCurrentlyActive
              ? "bg-[#007A2F] text-white"
              : "text-[#e6ffe6] hover:bg-[#007A2F]"
          }`}
        >
          <Icon size={18} className="mr-3" />
          <span className="flex-1">{label}</span>
          <span className="text-xs bg-orange-500 text-white px-2 py-1 rounded-full ml-2">
            Beta
          </span>
        </Link>
      );
    }

    return (
      <Link
        key={href}
        href={href}
        className={`flex items-center px-4 py-2 rounded-md ${
          isCurrentlyActive
            ? "bg-[#007A2F] text-white"
            : "text-[#e6ffe6] hover:bg-[#007A2F]"
        }`}
      >
        <Icon size={18} className="mr-3" />
        <span>{label}</span>
      </Link>
    );
  };

  return (
    <div className="min-h-screen bg-[#f8f9fa] flex">
      {/* Sidebar */}
      <div className="w-64 bg-[#009639] text-white flex flex-col h-screen fixed overflow-y-auto">
        <div className="p-4 flex items-center">
          <div className="bg-white rounded-md w-8 h-8 flex items-center justify-center mr-2">
            <span className="text-[#009639] font-bold">P</span>
          </div>
          <h1 className="text-xl font-bold">Poolly Admin</h1>
        </div>

        {/* MAIN Section */}
        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">MAIN</div>
          <nav className="space-y-1">
            {navigationItems.main.map(renderNavItem)}
          </nav>
        </div>

        {/* MANAGEMENT Section */}
        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">
            MANAGEMENT
          </div>
          <nav className="space-y-1">
            {navigationItems.management.map(renderNavItem)}
          </nav>
        </div>

        {/* MONITORING Section */}
        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">
            MONITORING
          </div>
          <nav className="space-y-1">
            {navigationItems.monitoring.map(renderNavItem)}
          </nav>
        </div>

        {/* INSIGHTS Section */}
        <div className="mt-6 px-4">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">
            INSIGHTS
          </div>
          <nav className="space-y-1">
            {navigationItems.insights.map(renderNavItem)}
          </nav>
        </div>

        {/* SYSTEM Section */}
        <div className="mt-6 px-4 mb-6">
          <div className="text-sm font-medium text-[#e6ffe6] mb-2">SYSTEM</div>
          <nav className="space-y-1">
            {navigationItems.system.map(renderNavItem)}
          </nav>
        </div>

        {/* User Profile Section */}
        <div className="mt-auto p-4 border-t border-[#007A2F]">
          <Link href="/profile">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-2">
                <span className="text-[#009639] font-medium">A</span>
              </div>
              <div>
                <p className="text-sm font-medium">Admin User</p>
                <p className="text-xs text-[#e6ffe6]"><EMAIL></p>
              </div>
            </div>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-64 flex-1">
        {/* Top Navigation */}
        <div className="bg-white h-16 border-b border-gray-200 flex items-center justify-between px-6">
          <div className="flex items-center">
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder="Search..."
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <button className="relative p-2 text-gray-500 hover:text-gray-700">
              <Bell size={20} />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>
            <button className="relative p-2 text-gray-500 hover:text-gray-700">
              <MessageSquare size={20} />
            </button>
            <div className="flex items-center">
              <Link href="/profile">
                <div className="w-8 h-8 rounded-full bg-[#009639] flex items-center justify-center text-white">
                  <span className="font-medium">A</span>
                </div>
              </Link>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="p-6">{children}</div>
      </div>
    </div>
  );
}
