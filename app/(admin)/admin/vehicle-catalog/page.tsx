"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  Car,
  DollarSign,
  Edit,
  Trash2,
  Eye,
  Settings,
  CheckCircle,
  XCircle,
  Calendar,
  Upload,
  Loader,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

import VehicleCatalogFormDialog from "./components/VehicleCatalogFormDialog";
import {
  getVehicleCatalogItems,
  createVehicleCatalogItem,
  updateVehicleCatalogItem,
  deleteVehicleCatalogItem,
  publishCatalogItemToListings,
  getVehicleCatalogFilterOptions,
  type VehicleCatalogItem,
  type VehicleCatalogSearchFilters,
} from "@/drizzle-actions/admin/vehicle-catalog";

import type { VehicleCatalogItem as LegacyVehicleCatalogItem } from "../types/vehicle-catalog";

export default function VehicleCatalogPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterFuelType, setFilterFuelType] = useState<string>("all");
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingVehicle, setEditingVehicle] =
    useState<VehicleCatalogItem | null>(null);
  const [dialogMode, setDialogMode] = useState<"add" | "edit" | "view">("add");
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [catalogItems, setCatalogItems] = useState<VehicleCatalogItem[]>([]);
  const [filterOptions, setFilterOptions] = useState<{
    categories: string[];
    fuelTypes: string[];
    makes: string[];
    yearRange: { min: number; max: number };
  }>({
    categories: [],
    fuelTypes: [],
    makes: [],
    yearRange: { min: 2020, max: new Date().getFullYear() },
  });

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch catalog items and filter options
  useEffect(() => {
    if (isClient) {
      fetchCatalogData();
      fetchFilterOptions();
    }
  }, [isClient, searchQuery, filterCategory, filterStatus, filterFuelType]);

  const fetchCatalogData = async () => {
    try {
      setIsLoading(true);
      const filters: VehicleCatalogSearchFilters = {};

      if (searchQuery) filters.searchQuery = searchQuery;
      if (filterCategory !== "all") filters.category = filterCategory;
      if (filterFuelType !== "all") filters.fuelType = filterFuelType;
      if (filterStatus === "ehailing") filters.ehailingEligible = true;
      if (filterStatus === "non-ehailing") filters.ehailingEligible = false;

      const items = await getVehicleCatalogItems(filters);
      setCatalogItems(items);
    } catch (error) {
      console.error("Error fetching catalog items:", error);
      toast.error("Failed to load catalog items");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFilterOptions = async () => {
    try {
      const options = await getVehicleCatalogFilterOptions();
      setFilterOptions(options);
    } catch (error) {
      console.error("Error fetching filter options:", error);
    }
  };

  // Convert database item to legacy format for compatibility
  const convertToLegacyFormat = (
    item: VehicleCatalogItem
  ): LegacyVehicleCatalogItem => {
    return {
      id: item.id,
      make: item.make,
      model: item.model,
      year: item.year,
      category: item.category as any,
      fuelType: item.fuelType,
      ehailingEligible: item.ehailingEligible,
      weeklyFeeTarget: item.weeklyFeeTarget,
      weeklyRate: item.weeklyFeeTarget || 0,
      initiationFee: 7500, // Default value
      features: item.features,
      platforms: item.platforms,
      suitableFor: item.platforms,
      isActive: item.ehailingEligible,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      description: item.description,
      images: item.images,
      imageUrl:
        item.images.find((img) => img.isPrimary)?.imageUrl ||
        item.images[0]?.imageUrl,
    };
  };

  // Filtering is now handled in fetchCatalogData, so we use catalogItems directly
  const filteredItems = catalogItems;

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "sedan":
        return "bg-blue-100 text-blue-800";
      case "suv":
        return "bg-green-100 text-green-800";
      case "hatchback":
        return "bg-purple-100 text-purple-800";
      case "bakkie":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleAddVehicle = () => {
    console.log("🔴 Before state update:", {
      isFormDialogOpen,
      dialogMode,
      editingVehicle,
    });

    setEditingVehicle(null);
    setDialogMode("add");
    setIsFormDialogOpen(true);
  };

  const handleEditVehicle = (vehicle: VehicleCatalogItem) => {
    console.log("🟡 handleEditVehicle called for vehicle:", vehicle.id);

    // Small delay to allow dropdown to close and clean up its portal first
    setTimeout(() => {
      setEditingVehicle(vehicle);
      setDialogMode("edit");
      setIsFormDialogOpen(true);
    }, 100);
  };

  const handleViewVehicle = (vehicle: VehicleCatalogItem) => {
    console.log("🟢 handleViewVehicle called for vehicle:", vehicle.id);

    // Small delay to allow dropdown to close and clean up its portal first
    setTimeout(() => {
      setEditingVehicle(vehicle);
      setDialogMode("view");
      setIsFormDialogOpen(true);
    }, 100);
  };

  const handleFormSubmit = async (vehicleData: LegacyVehicleCatalogItem) => {
    try {
      // Convert legacy format to database format
      const dbData = {
        make: vehicleData.make,
        model: vehicleData.model,
        year: vehicleData.year,
        category: vehicleData.category,
        fuelType: "petrol", // Default value, should be from form
        ehailingEligible: vehicleData.isActive || false,
        weeklyFeeTarget: vehicleData.weeklyRate,
        description: vehicleData.description,
        features: vehicleData.features,
        platforms: vehicleData.suitableFor || [],
        images: vehicleData.imageUrl
          ? [{ imageUrl: vehicleData.imageUrl, isPrimary: true }]
          : [],
      };

      if (dialogMode === "edit" && editingVehicle) {
        // Update existing item
        await updateVehicleCatalogItem(editingVehicle.id, dbData);
        toast.success("Vehicle catalog item updated successfully");
      } else {
        // Create new item
        await createVehicleCatalogItem(dbData);
        toast.success("Vehicle catalog item created successfully");
      }

      // Refresh the data
      await fetchCatalogData();
      handleCloseDialog();
    } catch (error) {
      console.error("Error saving vehicle catalog item:", error);
      toast.error("Failed to save vehicle catalog item");
    }
  };

  const handleDeleteVehicle = async (vehicleId: number) => {
    if (
      !confirm("Are you sure you want to delete this vehicle catalog item?")
    ) {
      return;
    }

    try {
      await deleteVehicleCatalogItem(vehicleId);
      toast.success("Vehicle catalog item deleted successfully");
      await fetchCatalogData();
    } catch (error) {
      console.error("Error deleting vehicle catalog item:", error);
      toast.error("Failed to delete vehicle catalog item");
    }
  };

  const handlePublishToListings = async (vehicleId: number) => {
    try {
      const result = await publishCatalogItemToListings(vehicleId);

      if (result.success) {
        toast.success(
          "Vehicle catalog item published to listings successfully"
        );
      } else {
        toast.error(result.error || "Failed to publish catalog item");
      }
    } catch (error) {
      console.error("Error publishing catalog item:", error);
      toast.error("Failed to publish catalog item");
    }
  };

  const handleCloseDialog = () => {
    console.log("handleCloseDialog called - closing dialog");
    setIsFormDialogOpen(false);

    // Safety cleanup: Ensure body pointer events are restored
    setTimeout(() => {
      document.body.style.pointerEvents = "";
      setEditingVehicle(null);
      setDialogMode("add");
    }, 150); // Small delay to allow dialog animation to complete
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Vehicle Catalog</h1>
        <Button
          onClick={handleAddVehicle}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Vehicle Model
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Models</p>
                <p className="text-2xl font-bold mt-1">{catalogItems.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Car className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Models</p>
                <p className="text-2xl font-bold mt-1">
                  {catalogItems.filter((item) => item.ehailingEligible).length}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg Weekly Rate</p>
                <p className="text-2xl font-bold mt-1">
                  R
                  {Math.round(
                    catalogItems.reduce(
                      (sum, item) => sum + (item.weeklyFeeTarget || 0),
                      0
                    ) / catalogItems.length
                  ).toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <DollarSign className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Categories</p>
                <p className="text-2xl font-bold mt-1">
                  {new Set(catalogItems.map((item) => item.category)).size}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                <Settings className="text-purple-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <CardTitle>Vehicle Models</CardTitle>
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search models..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by category"
              >
                <option value="all">All Categories</option>
                {filterOptions.categories.map((category) => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              <select
                value={filterFuelType}
                onChange={(e) => setFilterFuelType(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by fuel type"
              >
                <option value="all">All Fuel Types</option>
                {filterOptions.fuelTypes.map((fuelType) => (
                  <option key={fuelType} value={fuelType}>
                    {fuelType.charAt(0).toUpperCase() + fuelType.slice(1)}
                  </option>
                ))}
              </select>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by status"
              >
                <option value="all">All Status</option>
                <option value="ehailing">E-hailing Eligible</option>
                <option value="non-ehailing">Non E-hailing</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading catalog items...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Fuel Type</TableHead>
                    <TableHead>Pricing</TableHead>
                    <TableHead>Platforms</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        No catalog items found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {item.make} {item.model}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.year} {item.variant && `• ${item.variant}`}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={getCategoryColor(item.category)}
                          >
                            {item.category.charAt(0).toUpperCase() +
                              item.category.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className="text-xs">
                            {item.fuelType.charAt(0).toUpperCase() +
                              item.fuelType.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {item.weeklyFeeTarget
                                ? `R${item.weeklyFeeTarget.toLocaleString()}/week`
                                : "TBD"}
                            </div>
                            {item.estimatedPrice && (
                              <div className="text-sm text-gray-500">
                                Est: R{item.estimatedPrice.toLocaleString()}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {item.platforms.slice(0, 2).map((platform) => (
                              <Badge
                                key={platform}
                                variant="secondary"
                                className="text-xs"
                              >
                                {platform}
                              </Badge>
                            ))}
                            {item.platforms.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{item.platforms.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={
                              item.ehailingEligible
                                ? "bg-green-100 text-green-800 border-green-200"
                                : "bg-gray-100 text-gray-800 border-gray-200"
                            }
                          >
                            {item.ehailingEligible ? (
                              <>
                                <CheckCircle size={12} className="mr-1" />
                                E-hailing
                              </>
                            ) : (
                              <>
                                <XCircle size={12} className="mr-1" />
                                Standard
                              </>
                            )}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-gray-500">
                            <Calendar size={14} className="mr-1" />
                            <span>
                              {isClient && item.updatedAt
                                ? new Date(item.updatedAt).toLocaleDateString(
                                    "en-GB"
                                  )
                                : isClient
                                  ? "N/A"
                                  : "Loading..."}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewVehicle(item)}
                              className="h-8 w-8 p-0"
                              title="View Details"
                            >
                              <Eye size={16} className="text-gray-600" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditVehicle(item)}
                              className="h-8 w-8 p-0"
                              title="Edit"
                            >
                              <Edit size={16} className="text-gray-600" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePublishToListings(item.id)}
                              className="h-8 w-8 p-0"
                              title="Publish to Listings"
                            >
                              <Upload size={16} className="text-green-600" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteVehicle(item.id)}
                              className="h-8 w-8 p-0"
                              title="Delete"
                            >
                              <Trash2 size={16} className="text-red-600" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Catalog Form Dialog */}
      <VehicleCatalogFormDialog
        isOpen={isFormDialogOpen}
        onClose={handleCloseDialog}
        onConfirm={handleFormSubmit}
        editingVehicle={
          editingVehicle ? convertToLegacyFormat(editingVehicle) : null
        }
        mode={dialogMode}
      />
    </div>
  );
}
