"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { sendMessageToQueue } from "@/lib/sqsClient";
import outputs from "@/amplify_outputs.json";

export default function MessageSender() {
  const [name, setName] = useState("");
  const [templateName, setTemplateName] = useState(
    outputs.custom?.inviteEmail || ""
  );
  const [toEmails, setToEmails] = useState("");
  const [status, setStatus] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setIsLoading(true);
    setStatus("");

    try {
      const QUEUE_URL = outputs.custom?.EmailQueueUrl;
      if (!QUEUE_URL) throw new Error("QUEUE_URL not set in environment");

      if (!toEmails.trim()) throw new Error("Recipient email(s) required.");
      if (!name.trim()) throw new Error("Name is required.");

      // Convert comma-separated emails into array
      const emailArray = toEmails
        .split(",")
        .map((email) => email.trim())
        .filter((email) => email);

      if (emailArray.length === 0) {
        throw new Error("At least one valid email is required.");
      }

      const messageBody = {
        to: emailArray,
        templateName: "PoollyInviteTemplate",
        templateData: {
          name,
          content: `We're so glad you're here! We'll keep you in the loop with our latest news and special offers.`,
        },
      };

      await sendMessageToQueue(QUEUE_URL, messageBody);

      setStatus("Message sent successfully!");
      setName("");
      setToEmails("");
    } catch (error: any) {
      setStatus(`Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Send Invitation Email</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              placeholder="Recipient Emails (comma separated)"
              value={toEmails}
              onChange={(e) => setToEmails(e.target.value)}
              disabled={isLoading}
              required
            />
            <Input
              placeholder="Recipient Name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
              required
            />
            <Button
              type="submit"
              disabled={isLoading || !toEmails.trim() || !name.trim()}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                "Send Invitation"
              )}
            </Button>
            {status && (
              <Alert
                variant={status.includes("Error") ? "destructive" : "default"}
              >
                <AlertDescription>{status}</AlertDescription>
              </Alert>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
