"use client";

import React, { useState } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  User,
  Car,
  DollarSign,
  FileText,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Upload,
  Download,
  Edit,
  XCircle,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import type { AssignmentDetailsDialogProps } from "../../types/assignments";

export default function AssignmentDetailsDialog({
  isOpen,
  onClose,
  assignment,
  mode,
  onUpdate,
  onRecordPayment,
  onTerminate,
}: AssignmentDetailsDialogProps) {
  const [activeTab, setActiveTab] = useState("overview");
  const [isEditing, setIsEditing] = useState(mode === "edit");
  const [editedNotes, setEditedNotes] = useState(assignment?.notes || "");
  const [terminationReason, setTerminationReason] = useState("");
  const [showTerminationDialog, setShowTerminationDialog] = useState(false);

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  if (!assignment) return null;

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setIsEditing(false);
      setEditedNotes(assignment?.notes || "");
      setShowTerminationDialog(false);
      onClose();
    }
  };

  const handleSaveNotes = () => {
    if (onUpdate) {
      onUpdate({ ...assignment, notes: editedNotes });
    }
    setIsEditing(false);
  };

  const handleTerminate = () => {
    if (onTerminate && terminationReason.trim()) {
      onTerminate(assignment.id, terminationReason);
      setShowTerminationDialog(false);
      onClose();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "contract_uploaded":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending_setup":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "current":
        return "bg-green-100 text-green-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <User className="h-5 w-5 text-[#009639]" />
            Assignment Details - {assignment.driverName}
          </DialogTitle>
          <DialogDescription>
            Manage assignment details, payments, and status
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="financial">Financial</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Driver & Vehicle Info */}
            <div className="grid grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <User className="h-5 w-5 text-[#009639]" />
                    Driver Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm text-gray-500">Name</Label>
                    <p className="font-medium">{assignment.driverName}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-500">Email</Label>
                    <div className="flex items-center gap-2">
                      <Mail size={14} className="text-gray-400" />
                      <p className="text-sm">{assignment.driverEmail}</p>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-500">Phone</Label>
                    <div className="flex items-center gap-2">
                      <Phone size={14} className="text-gray-400" />
                      <p className="text-sm">{assignment.driverPhone}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Car className="h-5 w-5 text-[#009639]" />
                    Vehicle Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm text-gray-500">Vehicle</Label>
                    <p className="font-medium">{assignment.vehicleName}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-500">
                      Registration
                    </Label>
                    <p className="text-sm">{assignment.vehicleRegistration}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-500">
                      Assignment Date
                    </Label>
                    <div className="flex items-center gap-2">
                      <Calendar size={14} className="text-gray-400" />
                      <p className="text-sm">
                        {new Date(
                          assignment.assignmentDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Status & Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <CheckCircle className="h-5 w-5 text-[#009639]" />
                  Status & Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div>
                      <Label className="text-sm text-gray-500">
                        Assignment Status
                      </Label>
                      <div className="mt-1">
                        <Badge
                          variant="outline"
                          className={getStatusColor(assignment.status)}
                        >
                          {assignment.status
                            .replace("_", " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm text-gray-500">
                        Payment Status
                      </Label>
                      <div className="mt-1">
                        <Badge
                          variant="outline"
                          className={getPaymentStatusColor(
                            assignment.paymentStatus
                          )}
                        >
                          {assignment.paymentStatus.charAt(0).toUpperCase() +
                            assignment.paymentStatus.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {assignment.status !== "terminated" && (
                      <>
                        <Button
                          onClick={() => onRecordPayment?.(assignment.id)}
                          size="sm"
                          className="bg-[#009639] hover:bg-[#007A2F]"
                        >
                          <DollarSign size={16} className="mr-1" />
                          Record Payment
                        </Button>
                        <Button
                          onClick={() => setShowTerminationDialog(true)}
                          size="sm"
                          variant="destructive"
                        >
                          <XCircle size={16} className="mr-1" />
                          Terminate
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-[#009639]" />
                    Assignment Notes
                  </div>
                  <Button
                    onClick={() => setIsEditing(!isEditing)}
                    size="sm"
                    variant="outline"
                  >
                    <Edit size={16} className="mr-1" />
                    {isEditing ? "Cancel" : "Edit"}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <div className="space-y-3">
                    <Textarea
                      value={editedNotes}
                      onChange={(e) => setEditedNotes(e.target.value)}
                      placeholder="Add notes about this assignment..."
                      rows={4}
                    />
                    <Button
                      onClick={handleSaveNotes}
                      size="sm"
                      className="bg-[#009639] hover:bg-[#007A2F]"
                    >
                      Save Notes
                    </Button>
                  </div>
                ) : (
                  <p className="text-sm text-gray-600">
                    {assignment.notes || "No notes added yet."}
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="financial" className="space-y-6">
            {/* Financial Summary */}
            <div className="grid grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Weekly Rate</p>
                      <p className="text-2xl font-bold text-[#009639]">
                        R{assignment.weeklyRate.toLocaleString()}
                      </p>
                    </div>
                    <DollarSign className="text-[#009639]" size={24} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">
                        Initiation Fee Paid
                      </p>
                      <p className="text-2xl font-bold text-green-600">
                        R{assignment.initiationFeePaid.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">
                        of R{assignment.initiationFee.toLocaleString()}
                      </p>
                    </div>
                    <CheckCircle className="text-green-600" size={24} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">
                        Outstanding Balance
                      </p>
                      <p
                        className={`text-2xl font-bold ${assignment.outstandingBalance > 0 ? "text-red-600" : "text-green-600"}`}
                      >
                        R{assignment.outstandingBalance.toLocaleString()}
                      </p>
                    </div>
                    {assignment.outstandingBalance > 0 ? (
                      <AlertTriangle className="text-red-600" size={24} />
                    ) : (
                      <CheckCircle className="text-green-600" size={24} />
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Payment Schedule */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Next Payment Due</p>
                      <p className="text-sm text-gray-500">
                        {new Date(
                          assignment.nextPaymentDue
                        ).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-[#009639]">
                        R{assignment.weeklyRate.toLocaleString()}
                      </p>
                      <Badge
                        variant="outline"
                        className={getPaymentStatusColor(
                          assignment.paymentStatus
                        )}
                      >
                        {assignment.paymentStatus}
                      </Badge>
                    </div>
                  </div>
                  {assignment.lastPaymentDate && (
                    <div className="flex justify-between items-center p-3 border rounded-lg bg-green-50">
                      <div>
                        <p className="font-medium">Last Payment</p>
                        <p className="text-sm text-gray-500">
                          {new Date(
                            assignment.lastPaymentDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">
                          R{assignment.weeklyRate.toLocaleString()}
                        </p>
                        <Badge
                          variant="outline"
                          className="bg-green-100 text-green-800"
                        >
                          Paid
                        </Badge>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Contract & Documents</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText size={20} className="text-gray-400" />
                    <div>
                      <p className="font-medium">Signed Contract</p>
                      <p className="text-sm text-gray-500">
                        Vehicle lease agreement
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {assignment.contractUploaded ? (
                      <>
                        <CheckCircle size={16} className="text-green-500" />
                        <Button size="sm" variant="outline">
                          <Download size={16} className="mr-1" />
                          Download
                        </Button>
                      </>
                    ) : (
                      <>
                        <AlertTriangle size={16} className="text-red-500" />
                        <Button
                          size="sm"
                          className="bg-[#009639] hover:bg-[#007A2F]"
                        >
                          <Upload size={16} className="mr-1" />
                          Upload
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText size={20} className="text-gray-400" />
                    <div>
                      <p className="font-medium">Driver Documents</p>
                      <p className="text-sm text-gray-500">
                        License, ID, proof of residence
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {assignment.documentsComplete ? (
                      <>
                        <CheckCircle size={16} className="text-green-500" />
                        <span className="text-sm text-green-600">Complete</span>
                      </>
                    ) : (
                      <>
                        <AlertTriangle size={16} className="text-red-500" />
                        <span className="text-sm text-red-600">Incomplete</span>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3 p-3 border-l-4 border-green-500 bg-green-50">
                    <CheckCircle size={16} className="text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Assignment Created</p>
                      <p className="text-sm text-gray-500">
                        {new Date(
                          assignment.assignmentDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  {assignment.contractUploaded && (
                    <div className="flex items-start gap-3 p-3 border-l-4 border-blue-500 bg-blue-50">
                      <FileText size={16} className="text-blue-500 mt-0.5" />
                      <div>
                        <p className="font-medium">Contract Uploaded</p>
                        <p className="text-sm text-gray-500">
                          Signed contract received
                        </p>
                      </div>
                    </div>
                  )}
                  {assignment.lastPaymentDate && (
                    <div className="flex items-start gap-3 p-3 border-l-4 border-green-500 bg-green-50">
                      <DollarSign size={16} className="text-green-500 mt-0.5" />
                      <div>
                        <p className="font-medium">Payment Received</p>
                        <p className="text-sm text-gray-500">
                          R{assignment.weeklyRate.toLocaleString()} on{" "}
                          {new Date(
                            assignment.lastPaymentDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Termination Dialog */}
        {showTerminationDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">
                Terminate Assignment
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Please provide a reason for terminating this assignment:
              </p>
              <Textarea
                value={terminationReason}
                onChange={(e) => setTerminationReason(e.target.value)}
                placeholder="Reason for termination..."
                rows={3}
                className="mb-4"
              />
              <div className="flex gap-3 justify-end">
                <Button
                  variant="outline"
                  onClick={() => setShowTerminationDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleTerminate}
                  disabled={!terminationReason.trim()}
                >
                  Terminate Assignment
                </Button>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
