"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  Search,
  Filter,
  ChevronRight,
  Star,
  Briefcase,
  Shield,
  Plus,
  Crown,
  Tag,
} from "lucide-react";
import { getAllListingsDrizzle } from "@/drizzle-actions/listings";

// Interface for the actual data structure returned by getAllListingsDrizzle
interface ListingMediaRead {
  id: number;
  listingId: number;
  mediaPath: string;
  createdAt?: string;
  updatedAt?: string;
}

interface EnrichedListing {
  id: number;
  partyId: number;
  vehicleId: number;
  effectiveFrom: string;
  effectiveTo: string;
  fraction: number;
  askingPrice: number;
  condition: string;
  mileage?: number;
  listingType: string;
  audience: string;
  createdAt: string;
  updatedAt: string;
  // Additional enriched fields from the query
  vehicleVin?: string;
  vehicleColor?: string;
  vehicleYear?: number;
  modelName?: string;
  makeName?: string;
  ownerFirstName?: string;
  ownerLastName?: string;
  // Real fields for media and vehicle data
  media?: ListingMediaRead[];
  vehicle?: {
    model?: {
      make?: { name?: string };
      model?: string;
    };
    color?: string;
    manufacturingYear?: number;
    countryOfRegistration?: string;
    vin_number?: string;
    vehicle_registration?: string;
  };
  owner?: {
    first_name?: string;
    last_name?: string;
  };
}
import {
  getListingInterestCountsDrizzle,
  getUserInterestStatusDrizzle,
} from "@/drizzle-actions/listing-interest";
import ListingImage from "@/components/listing-image";
import ListingInterestButton from "@/components/listing-interest-button";
import { useCurrentUser } from "@/hooks/use-current-user";
import { useSearchParams } from "next/navigation";

// Helper function to format listing types for display
const formatListingType = (type: string | undefined | null): string => {
  if (!type) {
    return "Unknown Type";
  }
  
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "Short-term Lease";
    case "LONG_TERM_LEASE_OUT":
      return "Long-term Lease";
    case "CO_OWNERSHIP_SALE":
      return "Co-ownership Sale";
    default:
      return type.replace(/_/g, " ");
  }
};

// Helper function to get listing type color
const getListingTypeColor = (type: string | undefined | null): string => {
  if (!type) {
    return "bg-gray-100 text-gray-800";
  }
  
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "bg-blue-100 text-blue-800";
    case "LONG_TERM_LEASE_OUT":
      return "bg-purple-100 text-purple-800";
    case "CO_OWNERSHIP_SALE":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function OpportunitiesScreen() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { partyId: currentUserPartyId, isLoading: userLoading } =
    useCurrentUser();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("vehicles");
  const [sortBy, setSortBy] = useState<"effectiveFrom" | "askingPrice" | "createdAt">("effectiveFrom");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Handle tab query parameter
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam && ["vehicles", "business", "partners"].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);
  const [vehicleListings, setVehicleListings] = useState<EnrichedListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [interestCounts, setInterestCounts] = useState<Record<number, number>>(
    {}
  );
  const [userInterestStatus, setUserInterestStatus] = useState<
    Record<number, boolean>
  >({});

  // Fetch real listings when component mounts
  useEffect(() => {
    const fetchListings = async () => {
      try {
        setLoading(true);
        const result = await getAllListingsDrizzle({
          page: 1,
          limit: 10,
          // Show all types of listings in opportunities
          sortBy: sortBy === "effectiveFrom" ? "createdAt" : (sortBy as "createdAt" | "askingPrice"),
          sortOrder: sortOrder,
        });
        // Map the snake_case data to camelCase for the UI
        const enrichedListings: EnrichedListing[] = result.records.map((record: any) => ({
          id: record.id,
          partyId: record.party_id,
          vehicleId: record.vehicle_id,
          effectiveFrom: record.effective_from,
          effectiveTo: record.effective_to,
          fraction: record.fraction,
          askingPrice: record.asking_price,
          condition: record.condition,
          mileage: record.mileage,
          listingType: record.listing_type,
          audience: record.audience,
          createdAt: record.created_at || "",
          updatedAt: record.updated_at || "",
          // Use real vehicle data from the database
          vehicle: {
            model: {
              make: { name: record.vehicle?.model?.make?.name || "Unknown Make" },
              model: record.vehicle?.model?.model || "Unknown Model"
            },
            color: record.vehicle?.color || "Unknown",
            manufacturingYear: record.vehicle?.manufacturing_year || 0,
            countryOfRegistration: record.vehicle?.country_of_registration || "ZA"
          },
          // Use real media data from the database
          media: record.media || [],
          // Additional data for display
          vehicleVin: record.vehicle?.vin_number,
          vehicleColor: record.vehicle?.color,
          vehicleYear: record.vehicle?.manufacturing_year,
          modelName: record.vehicle?.model?.model,
          makeName: record.vehicle?.model?.make?.name,
          ownerFirstName: record.owner?.first_name,
          ownerLastName: record.owner?.last_name
        }));
        
        setVehicleListings(enrichedListings);

        // Fetch interest counts for all listings
        const listingIds = result.records.map((listing) => listing.id);
        if (listingIds.length > 0) {
          const counts = await getListingInterestCountsDrizzle(listingIds);
          setInterestCounts(counts);

          // Fetch user interest status if user is logged in
          if (currentUserPartyId) {
            const userStatus = await getUserInterestStatusDrizzle(
              listingIds,
              currentUserPartyId
            );
            setUserInterestStatus(userStatus);
          }
        }
      } catch (error) {
        console.error("Error fetching listings:", error);
      } finally {
        setLoading(false);
      }
    };

    if (!userLoading) {
      fetchListings();
    }
  }, [currentUserPartyId, userLoading, sortBy, sortOrder]);

  const businessOpportunities = [
    {
      id: 1,
      title: "Ride-sharing Partnership",
      description: "Use your vehicle for ride-sharing during idle hours",
      category: "Transportation",
      earnings: "R5,000-12,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: 2,
      title: "Food Delivery Service",
      description: "Partner with local restaurants for food delivery",
      category: "Food & Beverage",
      earnings: "R3,000-8,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
    {
      id: 3,
      title: "Corporate Transportation",
      description: "Provide transportation for corporate clients",
      category: "Business",
      earnings: "R8,000-20,000/month",
      image: "/placeholder.svg?height=120&width=200",
    },
  ];

  const partnerServices = [
    {
      id: 1,
      title: "Premium Insurance",
      description: "Comprehensive coverage for shared vehicles",
      provider: "Santam Insurance",
      icon: <Shield size={24} className="text-[#009639]" />,
    },
    {
      id: 2,
      title: "Mobile Maintenance",
      description: "On-demand vehicle maintenance and repairs",
      provider: "AutoZone Mobile Services",
      icon: <Briefcase size={24} className="text-[#009639]" />,
    },
    {
      id: 3,
      title: "Business Consulting",
      description: "Financial and legal advice for vehicle sharing",
      provider: "Standard Bank Business",
      icon: <Star size={24} className="text-[#009639]" />,
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 border-b border-[#007A2F]">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold text-white">Opportunities</h1>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={() => router.push("/list-vehicle")}
          >
            <Plus size={20} className="text-white" />
          </button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="p-4">
        <div className="flex space-x-2 mb-3">
          <div className="flex-1 bg-white rounded-full px-4 py-2 flex items-center shadow-md border border-gray-100">
            <Search size={18} className="text-[#797879] mr-2" />
            <input
              type="text"
              placeholder="Search opportunities"
              className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {/* <button className="bg-white p-2 rounded-full shadow-sm">
            <Filter size={18} className="text-[#333333]" />
          </button> */}
        </div>
        
        {/* Sorting Controls */}
        {activeTab === "vehicles" && (
          <div className="flex justify-between items-center bg-gray-50 rounded-lg px-4 py-2">
            <span className="text-xs text-[#797879]">
              {vehicleListings.length} listing{vehicleListings.length !== 1 ? 's' : ''} available
            </span>
            <div className="flex items-center space-x-2">
              <label className="text-xs text-[#797879]">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as "effectiveFrom" | "askingPrice" | "createdAt")}
                className="px-2 py-1 text-xs bg-white border border-gray-200 rounded focus:outline-none focus:border-[#009639]"
              >
                <option value="effectiveFrom">Date Listed</option>
                <option value="askingPrice">Price</option>
                <option value="createdAt">Date Created</option>
              </select>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as "asc" | "desc")}
                className="px-2 py-1 text-xs bg-white border border-gray-200 rounded focus:outline-none focus:border-[#009639]"
              >
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-[#f2f2f2] mb-4">
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "vehicles"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("vehicles")}
          >
            Vehicle Listings
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "business"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("business")}
          >
            Business
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "partners"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("partners")}
          >
            Partners
          </button>
        </div>
      </div>

      {/* Vehicle Listings Tab */}
      {activeTab === "vehicles" && (
        <div className="px-4 pb-8">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="text-[#797879]">Loading listings...</div>
            </div>
          ) : vehicleListings.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="text-[#797879] text-center mb-4">
                No vehicle listings available yet.
              </div>
              <button
                className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium"
                onClick={() => router.push("/list-vehicle")}
              >
                Be the first to list your vehicle
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {vehicleListings.map((listing) => (
                <div
                  key={listing.id}
                  className="ride-card overflow-hidden drop-shadow-md rounded-xl border border-gray-100"
                >
                  <div className="h-40 bg-[#f2f2f2] relative">
                    <ListingImage
                      media={listing.media}
                      alt={`${listing.vehicle?.model?.make?.name} ${listing.vehicle?.model?.model}`}
                      className="object-cover"
                    />
                    
                    {/* User's Own Listing Indicator */}
                    {currentUserPartyId && listing.partyId === currentUserPartyId && (
                      <div className="absolute top-3 left-3 bg-[#009639] text-white px-3 py-1 rounded-full text-xs font-medium flex items-center shadow-sm">
                        <Crown size={12} className="mr-1" />
                        Your Listing
                      </div>
                    )}
                    
                    {/* Listing Type Badge */}
                    <div className={`absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-medium shadow-sm ${getListingTypeColor(listing.listingType)}`}>
                      <div className="flex items-center">
                        <Tag size={12} className="mr-1" />
                        {formatListingType(listing.listingType)}
                      </div>
                    </div>
                    
                    {/* Ownership Percentage - Only for Co-ownership Sales */}
                    {listing.listingType === "CO_OWNERSHIP_SALE" && (
                      <div className="absolute bottom-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                        {Math.round(listing.fraction)}% Ownership
                      </div>
                    )}
                    
                    {/* Rating */}
                    <div className="absolute bottom-3 left-3 bg-white px-3 py-1 rounded-full text-xs font-medium shadow-sm flex items-center">
                      <Star size={12} className="text-[#ff5c00] mr-1" />
                      <span>4.5</span>
                      <span className="text-[#797879] ml-1">(12)</span>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="flex justify-between items-center mb-1">
                      <h3 className="text-[#333333] font-semibold">
                        {listing.vehicle?.model?.make?.name}{" "}
                        {listing.vehicle?.model?.model}
                      </h3>
                      <span className="text-lg font-bold text-[#009639]">
                        R{listing.askingPrice?.toLocaleString() || "0"}
                        {(listing.listingType === "SHORT_TERM_LEASE_OUT" || listing.listingType === "LONG_TERM_LEASE_OUT") && (
                          <span className="text-sm font-medium">/month</span>
                        )}
                      </span>
                    </div>

                    <p className="text-sm text-[#797879] mb-2">
                      {listing.vehicle?.color} •{" "}
                      {listing.vehicle?.manufacturingYear} • {listing.condition}
                    </p>

                    <div className="flex items-center mb-3">
                      <div className="text-xs text-[#797879]">
                        📍{" "}
                        {listing.vehicle?.countryOfRegistration ||
                          "Location not specified"}
                      </div>
                    </div>

                    {/* Interest Expression Section */}
                    <div className="flex items-center justify-between mb-3">
                      <ListingInterestButton
                        listingId={listing.id}
                        listingAuthorPartyId={listing.partyId}
                        currentUserPartyId={currentUserPartyId}
                        initialInterestCount={interestCounts[listing.id] || 0}
                        initialUserHasInterest={
                          userInterestStatus[listing.id] || false
                        }
                        variant="compact"
                      />
                    </div>

                    <button
                      className="ride-primary-btn w-full py-2 text-sm"
                      onClick={() => {
                        // If it's the user's own listing, go to owner view
                        if (currentUserPartyId && listing.partyId === currentUserPartyId) {
                          router.push(`/my-listing/${listing.id}`);
                        } else {
                          router.push(`/fraction-purchase/${listing.id}`);
                        }
                      }}
                    >
                      {currentUserPartyId && listing.partyId === currentUserPartyId 
                        ? "Manage Listing" 
                        : "View Details"
                      }
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Business Opportunities Tab */}
      {activeTab === "business" && (
        <div className="px-4 pb-8">
          <div className="space-y-4">
            {businessOpportunities.map((opportunity) => (
              <div
                key={opportunity.id}
                className="ride-card overflow-hidden drop-shadow-md rounded-xl border border-gray-100"
              >
                <div className="h-32 bg-[#f2f2f2] relative">
                  <Image
                    src={opportunity.image || "/placeholder.svg"}
                    alt={opportunity.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                    {opportunity.category}
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-[#333333] font-semibold">
                      {opportunity.title}
                    </h3>
                  </div>

                  <p className="text-sm text-[#797879] mb-2">
                    {opportunity.description}
                  </p>

                  <div className="flex items-center justify-between mb-3">
                    <div className="text-xs text-[#797879]">
                      Potential Earnings
                    </div>
                    <div className="text-sm font-bold text-[#009639]">
                      {opportunity.earnings}
                    </div>
                  </div>

                  <button
                    className="ride-primary-btn w-full py-2 text-sm"
                    onClick={() =>
                      router.push(`/business-opportunity/${opportunity.id}`)
                    }
                  >
                    Apply Now
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Partner Services Tab */}
      {activeTab === "partners" && (
        <div className="px-4 pb-8">
          <div className="space-y-3">
            {partnerServices.map((service) => (
              <div
                key={service.id}
                className="ride-card p-4 drop-shadow-md rounded-xl border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    {service.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-[#333333] font-medium">
                          {service.title}
                        </h4>
                        <p className="text-xs text-[#797879] mb-1">
                          {service.provider}
                        </p>
                        <p className="text-sm text-[#333333]">
                          {service.description}
                        </p>
                      </div>
                    </div>
                  </div>
                  <button className="text-[#009639] flex-shrink-0">
                    <ChevronRight size={20} className="text-[#009639]" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
