"use client";

import { useState } from "react";
import Image from "next/image";
import {
  ChevronRight,
  FileText,
  Clock,
  ArrowLeft,
  DollarSign,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { clear } from "console";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  image?: string;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
  leaseTerms: {
    ownershipTimeline: string;
    paymentOptions: string[];
  };
}

interface VehicleDiscoveryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: (selectedVehicle: Vehicle) => void;
}

export default function VehicleDiscoveryDrawer({
  isOpen,
  onClose,
  onNext,
}: VehicleDiscoveryDrawerProps) {
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const vehicles: Vehicle[] = [
    {
      id: 1,
      make: "Suzuki",
      model: "Dzire",
      year: 2023,
      weeklyRate: 2700,
      image: "/images/cars/suzuki-dzire.webp",
      requirements: {
        minDeposit: 7500,
        documents: [
          "ID Document",
          "Bank Statement - 3 months",
          "Proof of residence",
          "Driver's license",
          "PrDP (Professional driving permit)",
          "Police clearance certificate",
        ],
      },
      leaseTerms: {
        ownershipTimeline: "48 months",
        paymentOptions: ["Flexible payment options available"],
      },
    },
  ];

  // Auto-select the first (and only) vehicle
  const vehicle = vehicles[0];
  if (!selectedVehicle && vehicle) {
    setSelectedVehicle(vehicle);
  }

  const handleNext = () => {
    if (selectedVehicle) {
      onNext(selectedVehicle);
    }
  };

  const handleViewDetails = () => {
    setShowDetails(true);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Select Vehicle
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Choose a vehicle for e-hailing lease
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {vehicles.map((vehicle) => (
                <div key={vehicle.id} className="space-y-8">
                  {/* Vehicle Card */}
                  <div className="rounded-xl border border-gray-100 drop-shadow-md bg-white p-4">
                    {/* Vehicle Image */}
                    <div className="mb-3 h-32 overflow-hidden rounded-lg bg-gray-100 relative">
                      <Image
                        src={vehicle.image || "/placeholder.svg"}
                        alt={`${vehicle.make} ${vehicle.model}`}
                        className="object-contain"
                        fill
                      />
                    </div>

                    {/* Vehicle Info */}
                    <div className="mb-3">
                      <h3 className="font-semibold text-[#333333]">
                        {vehicle.make} {vehicle.model}
                      </h3>
                      <p className="text-sm text-[#797879]">
                        {vehicle.year} Model
                      </p>
                    </div>

                    {/* Weekly Rate */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[#797879]">
                        Weekly Rate
                      </span>
                      <div className="text-lg font-bold text-[#009639]">
                        <span className="text-base text-[#797879]">From</span>{" "}
                        <span className="text-lg font-bold text-[#009639]">
                          R{vehicle.weeklyRate.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    {/* View Details Button */}
                    <div className="mt-3">
                      <button
                        title="View Lease Details"
                        onClick={handleViewDetails}
                        className="w-full bg-[#009639] text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors"
                      >
                        View Lease Details
                      </button>
                    </div>
                  </div>

                  {/* Detail Cards - Show only when details are requested */}
                  {showDetails && (
                    <div className="space-y-6 animate-in slide-in-from-bottom-4 duration-500">
                      {/* Initial Fee Card */}
                      <div>
                        <div className="flex items-center mb-3">
                          <DollarSign
                            size={18}
                            className="mr-2 text-[#009639]"
                          />
                          <h4 className="font-semibold text-[#333333]">
                            Initial Fee
                          </h4>
                        </div>
                        <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                          <div className="rounded-lg bg-[#f8fff8] border border-[#e6ffe6] p-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-[#797879]">
                                One-time payment
                              </span>
                              <span className="text-xl font-bold text-[#009639]">
                                R
                                {vehicle.requirements.minDeposit.toLocaleString()}
                              </span>
                            </div>
                            <p className="text-xs text-[#797879]">
                              Flexible payment options available
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Required Documents Card */}
                      <div>
                        <div className="flex items-center mb-3">
                          <FileText size={18} className="mr-2 text-[#009639]" />
                          <h4 className="font-semibold text-[#333333]">
                            Required Documents
                          </h4>
                        </div>
                        <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                          <div className="space-y-2">
                            {vehicle.requirements.documents.map(
                              (doc, index) => (
                                <div
                                  key={index}
                                  className="flex items-center text-sm text-[#797879]"
                                >
                                  <div className="w-2 h-2 bg-[#009639] rounded-full mr-3"></div>
                                  {doc}
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Process Overview Card */}
                      <div>
                        <div className="flex items-center mb-3">
                          <Clock size={18} className="mr-2 text-[#009639]" />
                          <h4 className="font-semibold text-[#333333]">
                            Process Overview
                          </h4>
                        </div>
                        <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                          <div className="space-y-3">
                            {[
                              "Submit application",
                              "Receive approval",
                              "Pay initiation fee",
                              "Sign lease",
                              "Receive car",
                            ].map((step, index) => (
                              <div
                                key={index}
                                className="flex items-center text-sm text-[#797879]"
                              >
                                <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                                  {index + 1}
                                </div>
                                {step}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Lease Terms Card */}
                      <div>
                        <div className="flex items-center mb-3">
                          <Clock size={18} className="mr-2 text-[#009639]" />
                          <h4 className="font-semibold text-[#333333]">
                            Lease Terms
                          </h4>
                        </div>
                        <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                          <div className="rounded-lg bg-gray-50 p-3">
                            <div className="flex items-center">
                              <Clock
                                size={16}
                                className="mr-2 text-[#009639]"
                              />
                              <span className="text-sm text-[#797879]">
                                {vehicle.leaseTerms.ownershipTimeline} ownership
                                timeline
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={!showDetails}
              className={`flex w-full items-center justify-center rounded-full py-3 font-semibold transition-all ${
                showDetails
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              }`}
            >
              Next
              <ChevronRight size={20} className="ml-2" />
            </button>
            {!showDetails && (
              <p className="text-xs text-[#797879] text-center mt-2">
                Please view lease details to continue
              </p>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
