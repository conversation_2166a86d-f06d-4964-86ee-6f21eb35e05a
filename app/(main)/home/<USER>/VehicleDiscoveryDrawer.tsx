"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import {
  ChevronRight,
  FileText,
  Clock,
  ArrowLeft,
  DollarSign,
  Car,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { getPublishedCatalogVehicles } from "@/drizzle-actions/admin/vehicle-catalog";
import { submitEhailingApplication } from "@/actions/applications";
import ApplicationDocumentUploader, {
  type UploadedDocument,
} from "@/components/ApplicationDocumentUploader";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  image?: string;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
  leaseTerms: {
    ownershipTimeline: string;
    paymentOptions: string[];
  };
  // Catalog-specific properties
  category?: string;
  fuelType?: string;
  ehailingEligible?: boolean;
  estimatedPrice?: number;
  weeklyFeeTarget?: number;
  description?: string;
  features?: string[];
  platforms?: string[];
  images?: Array<{
    id: number;
    imageUrl: string;
    isPrimary: boolean;
  }>;
}

interface InitiationFeeData {
  needsPaymentArrangement: boolean;
}

interface ExperienceData {
  hasExperience: boolean;
  duration?: string;
  company?: string;
  reasonStopped?: string;
  workType?: "part-time" | "full-time";
  profileNumber?: string;
}

interface VehicleDiscoveryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onApplicationSubmitted?: () => void; // New callback for when application is submitted
}

export default function VehicleDiscoveryDrawer({
  isOpen,
  onClose,
  onApplicationSubmitted,
}: VehicleDiscoveryDrawerProps) {
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Application flow state
  const [currentStep, setCurrentStep] = useState<
    "discovery" | "documents" | "initiation" | "experience"
  >("discovery");

  // Application form state
  const [documents, setDocuments] = useState<UploadedDocument[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [applicationId, setApplicationId] = useState<number | null>(null);

  const [initiationFee, setInitiationFee] = useState<InitiationFeeData>({
    needsPaymentArrangement: false,
  });

  const [experience, setExperience] = useState<ExperienceData>({
    hasExperience: false,
  });

  // Transform catalog vehicle to drawer format
  const transformCatalogVehicle = (catalogVehicle: any): Vehicle => {
    return {
      id: catalogVehicle.id,
      make: catalogVehicle.make,
      model: catalogVehicle.model,
      year: catalogVehicle.year,
      weeklyRate: catalogVehicle.weeklyFeeTarget || 2700,
      weeklyFeeTarget: catalogVehicle.weeklyFeeTarget,
      estimatedPrice: catalogVehicle.estimatedPrice,
      category: catalogVehicle.category,
      fuelType: catalogVehicle.fuelType,
      ehailingEligible: catalogVehicle.ehailingEligible,
      description: catalogVehicle.description,
      features: catalogVehicle.features,
      platforms: catalogVehicle.platforms,
      images: catalogVehicle.images,
      requirements: {
        minDeposit: 7500, // Default value, could be from listing details
        documents: [
          "ID Document",
          "Bank Statement - 3 months",
          "Proof of residence",
          "Driver's license",
          "PrDP (Professional driving permit)",
          "Police clearance certificate",
        ],
      },
      leaseTerms: {
        ownershipTimeline: "48 months",
        paymentOptions: ["Flexible payment options available"],
      },
    };
  };

  // Fetch published catalog vehicles
  useEffect(() => {
    const fetchVehicles = async () => {
      if (!isOpen) return; // Only fetch when drawer is opened

      setIsLoading(true);
      setError(null);

      try {
        const catalogVehicles = await getPublishedCatalogVehicles();
        const transformedVehicles = catalogVehicles.map(
          transformCatalogVehicle
        );
        setVehicles(transformedVehicles);
      } catch (err) {
        console.error("Error fetching catalog vehicles:", err);
        setError("Failed to load vehicles. Please try again.");
        // Fallback to empty array or could use hardcoded fallback
        setVehicles([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVehicles();
  }, [isOpen]);

  // Auto-select the first (and only) vehicle
  const vehicle = vehicles[0];
  if (!selectedVehicle && vehicle) {
    setSelectedVehicle(vehicle);
  }

  // Application form handlers
  const handleExperienceChange = (field: keyof ExperienceData, value: any) => {
    setExperience((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Form validation
  const canSubmitApplication = () => {
    if (!selectedVehicle?.id) return false;

    // Experience validation
    if (experience.hasExperience) {
      return !!(
        experience.duration &&
        experience.company &&
        experience.workType &&
        experience.profileNumber
      );
    }

    return true; // No experience required
  };

  // Handle form submission
  const handleSubmitApplication = async () => {
    if (!selectedVehicle?.id) {
      setSubmitError("No vehicle selected");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const formData = new FormData();
      formData.append("listingId", selectedVehicle.id.toString());
      formData.append(
        "hasEhailingExperience",
        experience.hasExperience.toString()
      );

      if (experience.hasExperience) {
        formData.append("ehailingCompany", experience.company || "");
        formData.append(
          "ehailingProfileNumber",
          experience.profileNumber || ""
        );
        formData.append("ehailingWorkType", experience.workType || "");
        formData.append("drivingExperienceYears", experience.duration || "");
      }

      formData.append(
        "arrangementRequested",
        initiationFee.needsPaymentArrangement.toString()
      );

      const result = await submitEhailingApplication(null, formData);

      if (result.success && result.applicationId) {
        setApplicationId(result.applicationId);
        onApplicationSubmitted?.();
        onClose(); // Close the drawer after successful submission
      } else {
        setSubmitError(result.error || "Failed to submit application");
      }
    } catch (error) {
      console.error("Application submission failed:", error);
      setSubmitError("Failed to submit application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    if (currentStep === "discovery" && selectedVehicle && showDetails) {
      // Initialize documents for the selected vehicle
      const requiredDocs: UploadedDocument[] = [
        {
          name: "Driver's License",
          documentType: "drivers_license",
          uploaded: false,
          required: true,
        },
        {
          name: "ID Document",
          documentType: "id_document",
          uploaded: false,
          required: true,
        },
        {
          name: "Proof of residence",
          documentType: "proof_of_residence",
          uploaded: false,
          required: true,
        },
        {
          name: "Selfie",
          documentType: "selfie",
          uploaded: false,
          required: true,
        },
      ];
      setDocuments(requiredDocs);
      setCurrentStep("documents");
    } else if (currentStep === "documents") {
      setCurrentStep("initiation");
    } else if (currentStep === "initiation") {
      setCurrentStep("experience");
    } else if (currentStep === "experience" && canSubmitApplication()) {
      handleSubmitApplication();
    }
  };

  const handleBack = () => {
    if (currentStep === "experience") {
      setCurrentStep("initiation");
    } else if (currentStep === "initiation") {
      setCurrentStep("documents");
    } else if (currentStep === "documents") {
      setCurrentStep("discovery");
    } else {
      onClose();
    }
  };

  const handleViewDetails = () => {
    setShowDetails(true);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  {currentStep === "discovery"
                    ? "Select Vehicle"
                    : "Application Process"}
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {currentStep === "discovery"
                    ? "Choose a vehicle for e-hailing lease"
                    : currentStep === "documents"
                      ? "Upload your required documents"
                      : currentStep === "initiation"
                        ? "Review initiation fee and payment options"
                        : "Tell us about your e-hailing experience"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator - Show only during application steps */}
          {currentStep !== "discovery" && (
            <div className="px-6 py-4 bg-gray-50 border-b">
              <div className="flex items-center justify-between text-xs">
                <span
                  className={
                    currentStep === "documents"
                      ? "text-[#009639] font-medium"
                      : "text-gray-600"
                  }
                >
                  Documents
                </span>
                <span
                  className={
                    currentStep === "initiation"
                      ? "text-[#009639] font-medium"
                      : "text-gray-600"
                  }
                >
                  Initiation Fee
                </span>
                <span
                  className={
                    currentStep === "experience"
                      ? "text-[#009639] font-medium"
                      : "text-gray-600"
                  }
                >
                  Experience
                </span>
              </div>

              {/* Progress Bar */}
              <div className="mt-3 h-1 bg-gray-200 rounded-full">
                <div
                  className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                  style={{
                    width:
                      currentStep === "documents"
                        ? "33%"
                        : currentStep === "initiation"
                          ? "66%"
                          : "100%",
                  }}
                />
              </div>
            </div>
          )}

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {currentStep === "discovery" ? (
              // Vehicle Discovery Content
              isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
                    <p className="text-[#797879]">Loading vehicles...</p>
                  </div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-red-500 mb-4">
                      <svg
                        className="w-12 h-12 mx-auto"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.694-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                    </div>
                    <p className="text-[#797879] mb-4">{error}</p>
                    <button
                      onClick={() => window.location.reload()}
                      className="px-4 py-2 bg-[#009639] text-white rounded-lg hover:bg-[#007A2F] transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              ) : vehicles.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-[#797879] mb-4 w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center">
                      <Car />
                    </div>
                    <p className="text-[#797879]">
                      No vehicles available at the moment
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {vehicles.map((vehicle) => (
                    <div key={vehicle.id} className="space-y-8">
                      {/* Vehicle Card */}
                      <div className="rounded-xl border border-gray-100 drop-shadow-md bg-white p-4">
                        {/* Vehicle Image */}
                        <div className="mb-3 h-32 overflow-hidden rounded-lg bg-gray-100 relative">
                          <Image
                            src={
                              vehicle.images?.find((img) => img.isPrimary)
                                ?.imageUrl ||
                              vehicle.images?.[0]?.imageUrl ||
                              vehicle.image ||
                              "/images/cars/suzuki-dzire.webp"
                            }
                            alt={`${vehicle.make} ${vehicle.model}`}
                            className="object-contain"
                            fill
                          />
                        </div>

                        {/* Vehicle Info */}
                        <div className="mb-3">
                          <h3 className="font-semibold text-[#333333]">
                            {vehicle.make} {vehicle.model}
                          </h3>
                          {vehicle.category && (
                            <p className="text-sm text-[#797879] capitalize">
                              {vehicle.category}
                            </p>
                          )}
                        </div>

                        {/* Weekly Rate */}
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-[#797879]">
                            Weekly Rate
                          </span>
                          <div className="text-lg font-bold text-[#009639]">
                            <span className="text-base text-[#797879]">
                              From
                            </span>{" "}
                            <span className="text-lg font-bold text-[#009639]">
                              R
                              {(
                                vehicle.weeklyFeeTarget || vehicle.weeklyRate
                              ).toLocaleString()}
                            </span>
                          </div>
                        </div>

                        {/* View Details Button */}
                        <div className="mt-3">
                          <button
                            title="View Lease Details"
                            onClick={handleViewDetails}
                            className="w-full bg-[#009639] text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors"
                          >
                            View Lease Details
                          </button>
                        </div>
                      </div>

                      {/* Detail Cards - Show only when details are requested */}
                      {showDetails && (
                        <div className="space-y-6 animate-in slide-in-from-bottom-4 duration-500">
                          {/* Initial Fee Card */}
                          <div>
                            <div className="flex items-center mb-3">
                              <DollarSign
                                size={18}
                                className="mr-2 text-[#009639]"
                              />
                              <h4 className="font-semibold text-[#333333]">
                                Initial Fee
                              </h4>
                            </div>
                            <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                              <div className="rounded-lg bg-[#f8fff8] border border-[#e6ffe6] p-3">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm text-[#797879]">
                                    One-time payment
                                  </span>
                                  <span className="text-xl font-bold text-[#009639]">
                                    R
                                    {vehicle.requirements.minDeposit.toLocaleString()}
                                  </span>
                                </div>
                                <p className="text-xs text-[#797879]">
                                  Flexible payment options available
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Required Documents Card */}
                          <div>
                            <div className="flex items-center mb-3">
                              <FileText
                                size={18}
                                className="mr-2 text-[#009639]"
                              />
                              <h4 className="font-semibold text-[#333333]">
                                Required Documents
                              </h4>
                            </div>
                            <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                              <div className="space-y-2">
                                {vehicle.requirements.documents.map(
                                  (doc, index) => (
                                    <div
                                      key={index}
                                      className="flex items-center text-sm text-[#797879]"
                                    >
                                      <div className="w-2 h-2 bg-[#009639] rounded-full mr-3"></div>
                                      {doc}
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Process Overview Card */}
                          <div>
                            <div className="flex items-center mb-3">
                              <Clock
                                size={18}
                                className="mr-2 text-[#009639]"
                              />
                              <h4 className="font-semibold text-[#333333]">
                                Process Overview
                              </h4>
                            </div>
                            <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                              <div className="space-y-3">
                                {[
                                  "Submit application",
                                  "Receive approval",
                                  "Pay initiation fee",
                                  "Sign lease",
                                  "Receive car",
                                ].map((step, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center text-sm text-[#797879]"
                                  >
                                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                                      {index + 1}
                                    </div>
                                    {step}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>

                          {/* Lease Terms Card */}
                          <div>
                            <div className="flex items-center mb-3">
                              <Clock
                                size={18}
                                className="mr-2 text-[#009639]"
                              />
                              <h4 className="font-semibold text-[#333333]">
                                Lease Terms
                              </h4>
                            </div>
                            <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                              <div className="rounded-lg bg-gray-50 p-3">
                                <div className="flex items-center">
                                  <Clock
                                    size={16}
                                    className="mr-2 text-[#009639]"
                                  />
                                  <span className="text-sm text-[#797879]">
                                    {vehicle.leaseTerms.ownershipTimeline}{" "}
                                    ownership timeline
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )
            ) : (
              // Application Steps Content
              <div className="space-y-6">
                {/* Vehicle Summary */}
                {selectedVehicle && (
                  <div className="rounded-xl border border-gray-100 drop-shadow-md bg-white p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-12 bg-gray-100 rounded-lg overflow-hidden relative">
                        <Image
                          src={
                            selectedVehicle.images?.find((img) => img.isPrimary)
                              ?.imageUrl ||
                            selectedVehicle.images?.[0]?.imageUrl ||
                            selectedVehicle.image ||
                            "/images/cars/suzuki-dzire.webp"
                          }
                          alt={`${selectedVehicle.make} ${selectedVehicle.model}`}
                          className="object-contain"
                          fill
                        />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-[#333333]">
                          {selectedVehicle.make} {selectedVehicle.model}
                        </h3>
                        <span className="text-sm text-[#797879]">
                          R{selectedVehicle.weeklyRate.toLocaleString()}/week
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Documents Step */}
                {currentStep === "documents" && (
                  <div className="space-y-6">
                    <ApplicationDocumentUploader
                      applicationType="ehailing"
                      applicationId={applicationId || undefined}
                      documents={documents}
                      setDocuments={setDocuments}
                      onError={(error) => setSubmitError(error)}
                    />
                  </div>
                )}

                {/* Error Display */}
                {submitError && (
                  <Alert>
                    <AlertDescription className="text-red-600">
                      {submitError}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Initiation Fee Step */}
                {currentStep === "initiation" && (
                  <div className="space-y-6">
                    <div>
                      <div className="flex items-center mb-3">
                        <DollarSign size={18} className="mr-2 text-[#009639]" />
                        <h4 className="font-semibold text-[#333333]">
                          Initiation Fee
                        </h4>
                      </div>
                      <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                        <div className="space-y-4">
                          {/* Fee Display */}
                          <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h5 className="font-semibold text-[#007A2F]">
                                  Initiation Fee
                                </h5>
                                <p className="text-xs text-[#007A2F] mt-1">
                                  One-time payment to start your lease
                                </p>
                              </div>
                              <div className="text-right">
                                <span className="text-2xl font-bold text-[#009639]">
                                  R
                                  {selectedVehicle?.requirements.minDeposit.toLocaleString()}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Payment Arrangement Option */}
                          <div className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start">
                              <input
                                type="checkbox"
                                id="paymentArrangement"
                                checked={initiationFee.needsPaymentArrangement}
                                onChange={(e) =>
                                  setInitiationFee((prev) => ({
                                    ...prev,
                                    needsPaymentArrangement: e.target.checked,
                                  }))
                                }
                                className="mt-1 mr-3"
                              />
                              <div className="flex-1">
                                <label
                                  htmlFor="paymentArrangement"
                                  className="text-sm font-medium text-[#333333] cursor-pointer"
                                >
                                  I need to make a payment arrangement for the
                                  initiation fee
                                </label>
                                <p className="text-xs text-[#797879] mt-1">
                                  Check this if you'd like to discuss payment
                                  options or installments for the initiation
                                  fee.
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Payment Information */}
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h6 className="font-medium text-[#333333] mb-2">
                              Payment Information
                            </h6>
                            <ul className="text-xs text-[#797879] space-y-1">
                              <li>
                                • The initiation fee is due before vehicle
                                handover
                              </li>
                              <li>
                                • Payment can be made via bank transfer or card
                              </li>
                              <li>
                                • Payment arrangements are subject to approval
                              </li>
                              <li>• Contact support for payment assistance</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Experience Step */}
                {currentStep === "experience" && (
                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold text-[#333333] mb-3">
                        E-hailing Experience
                      </h4>
                      <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                        <div className="space-y-4">
                          {/* Has Experience Question */}
                          <div>
                            <label className="text-sm font-medium text-[#333333] mb-2 block">
                              Have you driven for e-hailing before or currently?
                            </label>
                            <div className="flex space-x-4">
                              <label className="flex items-center">
                                <input
                                  type="radio"
                                  name="hasExperience"
                                  checked={experience.hasExperience === true}
                                  onChange={() =>
                                    handleExperienceChange(
                                      "hasExperience",
                                      true
                                    )
                                  }
                                  className="mr-2"
                                />
                                <span className="text-sm text-[#333333]">
                                  Yes
                                </span>
                              </label>
                              <label className="flex items-center">
                                <input
                                  type="radio"
                                  name="hasExperience"
                                  checked={experience.hasExperience === false}
                                  onChange={() =>
                                    handleExperienceChange(
                                      "hasExperience",
                                      false
                                    )
                                  }
                                  className="mr-2"
                                />
                                <span className="text-sm text-[#333333]">
                                  No
                                </span>
                              </label>
                            </div>
                          </div>

                          {/* Experience Details - Show only if has experience */}
                          {experience.hasExperience && (
                            <div className="space-y-4 pt-4 border-t border-gray-200">
                              {/* Duration */}
                              <div>
                                <label className="text-sm font-medium text-[#333333] mb-1 block">
                                  How long have you been driving?
                                </label>
                                <select
                                  title="Duration"
                                  value={experience.duration || ""}
                                  onChange={(e) =>
                                    handleExperienceChange(
                                      "duration",
                                      e.target.value
                                    )
                                  }
                                  className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                                >
                                  <option value="">Select duration</option>
                                  <option value="Less than 1 year">
                                    Less than 1 year
                                  </option>
                                  <option value="1-2 years">1-2 years</option>
                                  <option value="2-3 years">2-3 years</option>
                                  <option value="3-5 years">3-5 years</option>
                                  <option value="More than 5 years">
                                    More than 5 years
                                  </option>
                                </select>
                              </div>

                              {/* Company */}
                              <div>
                                <label className="text-sm font-medium text-[#333333] mb-1 block">
                                  Which company?
                                </label>
                                <select
                                  title="Company"
                                  value={experience.company || ""}
                                  onChange={(e) =>
                                    handleExperienceChange(
                                      "company",
                                      e.target.value
                                    )
                                  }
                                  className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                                >
                                  <option value="">Select company</option>
                                  <option value="Uber">Uber</option>
                                  <option value="Bolt">Bolt</option>
                                  <option value="InDriver">InDriver</option>
                                  <option value="Other">Other</option>
                                </select>
                              </div>

                              {/* Work Type */}
                              <div>
                                <label className="text-sm font-medium text-[#333333] mb-2 block">
                                  Work Type
                                </label>
                                <div className="flex space-x-4">
                                  <label className="flex items-center">
                                    <input
                                      type="radio"
                                      name="workType"
                                      checked={
                                        experience.workType === "part-time"
                                      }
                                      onChange={() =>
                                        handleExperienceChange(
                                          "workType",
                                          "part-time"
                                        )
                                      }
                                      className="mr-2"
                                    />
                                    <span className="text-sm text-[#333333]">
                                      Part-time
                                    </span>
                                  </label>
                                  <label className="flex items-center">
                                    <input
                                      type="radio"
                                      name="workType"
                                      checked={
                                        experience.workType === "full-time"
                                      }
                                      onChange={() =>
                                        handleExperienceChange(
                                          "workType",
                                          "full-time"
                                        )
                                      }
                                      className="mr-2"
                                    />
                                    <span className="text-sm text-[#333333]">
                                      Full-time
                                    </span>
                                  </label>
                                </div>
                              </div>

                              {/* Profile Number */}
                              <div>
                                <label className="text-sm font-medium text-[#333333] mb-1 block">
                                  Profile Number
                                </label>
                                <input
                                  type="text"
                                  value={experience.profileNumber || ""}
                                  onChange={(e) =>
                                    handleExperienceChange(
                                      "profileNumber",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Enter your profile number"
                                  className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                (currentStep === "discovery" && !showDetails) ||
                (currentStep === "experience" && !canSubmitApplication()) ||
                isSubmitting
              }
              className={`flex w-full items-center justify-center rounded-full py-3 font-semibold transition-all ${
                (currentStep === "discovery" && !showDetails) ||
                (currentStep === "experience" && !canSubmitApplication()) ||
                isSubmitting
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : "bg-[#009639] text-white hover:bg-[#007A2F]"
              }`}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Submitting...
                </>
              ) : currentStep === "discovery" ? (
                <>
                  Apply Now
                  <ChevronRight size={20} className="ml-2" />
                </>
              ) : currentStep === "documents" ? (
                <>
                  Continue to Initiation Fee
                  <ChevronRight size={20} className="ml-2" />
                </>
              ) : currentStep === "initiation" ? (
                <>
                  Continue to Experience
                  <ChevronRight size={20} className="ml-2" />
                </>
              ) : (
                "Submit Application"
              )}
            </button>
            {currentStep === "discovery" && !showDetails && (
              <p className="text-xs text-[#797879] text-center mt-2">
                Please view lease details to continue
              </p>
            )}
            {currentStep === "experience" &&
              !canSubmitApplication() &&
              !isSubmitting && (
                <p className="text-xs text-[#797879] text-center mt-2">
                  Please complete all required fields
                </p>
              )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
