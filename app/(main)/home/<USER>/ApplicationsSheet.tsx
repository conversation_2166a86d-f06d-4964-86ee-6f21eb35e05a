"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  FileText,
  Calendar,
  Clock,
  ChevronRight,
  Car,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import ApplicationStatusSheet from "./ApplicationStatusSheet";
import { FetchUserAttributesOutput } from "aws-amplify/auth";
import { getUserApplicationsAction } from "@/actions/applications";

interface Application {
  id: number;
  type: "ehailing" | "rental" | "fractional";
  listingId: number;
  applicantId: number;
  submittedAt: Date;
  vehicleInfo?: {
    make: string;
    model: string;
    year: number;
    weeklyRate?: number;
    monthlyRate?: number;
    image: string;
  };
  listingDetails?: any;
}

interface ApplicationsSheetProps {
  isOpen: boolean;
  onClose: () => void;
  attributes: FetchUserAttributesOutput;
}

export default function ApplicationsSheet({
  isOpen,
  onClose,
  attributes,
}: ApplicationsSheetProps) {
  const [selectedApplication, setSelectedApplication] =
    useState<Application | null>(null);
  const [showStatusSheet, setShowStatusSheet] = useState(false);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchApplications();
    }
  }, [isOpen]);

  const fetchApplications = async () => {
    setLoading(true);
    try {
      const result = await getUserApplicationsAction();
      if (result.success && result.applications) {
        setApplications(result.applications);
      }
    } catch (error) {
      console.error("Error fetching applications:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return {
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          text: "text-yellow-800",
          color: "#ffd700",
        };
      case "under_review":
        return {
          bg: "bg-blue-50",
          border: "border-blue-200",
          text: "text-blue-800",
          color: "#3b82f6",
        };
      case "approved":
        return {
          bg: "bg-green-50",
          border: "border-green-200",
          text: "text-green-800",
          color: "#10b981",
        };
      case "rejected":
        return {
          bg: "bg-red-50",
          border: "border-red-200",
          text: "text-red-800",
          color: "#ef4444",
        };
      case "withdrawn":
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
          color: "#6b7280",
        };
      default:
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
          color: "#6b7280",
        };
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending Review";
      case "under_review":
        return "Under Review";
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      case "withdrawn":
        return "Withdrawn";
      default:
        return status;
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleApplicationClick = (application: Application) => {
    setSelectedApplication(application);
    setShowStatusSheet(true);
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full max-w-md p-0">
          <div className="flex h-full flex-col">
            {/* Header */}
            <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
              <div className="flex items-center">
                <button
                  title="Back"
                  onClick={onClose}
                  className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                >
                  <ArrowLeft size={24} />
                </button>
                <div>
                  <SheetTitle className="text-xl font-bold text-white">
                    Applications
                  </SheetTitle>
                  <SheetDescription className="text-sm text-green-100">
                    Track your application status
                  </SheetDescription>
                </div>
              </div>
            </SheetHeader>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
                    <p className="text-sm text-gray-500">
                      Loading applications...
                    </p>
                  </div>
                ) : applications.length === 0 ? (
                  <div className="text-center py-12">
                    <FileText
                      size={48}
                      className="mx-auto text-gray-400 mb-4"
                    />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Applications Yet
                    </h3>
                    <p className="text-gray-500">
                      Your applications will appear here once you submit them.
                    </p>
                  </div>
                ) : (
                  applications.map((application) => {
                    const getApplicationTitle = () => {
                      if (!application.vehicleInfo)
                        return `Application #${application.id}`;
                      return `${application.vehicleInfo.make} ${application.vehicleInfo.model} ${application.vehicleInfo.year}`;
                    };

                    const getApplicationSubtitle = () => {
                      return (
                        application.type.charAt(0).toUpperCase() +
                        application.type.slice(1) +
                        " Application"
                      );
                    };

                    // Get current status from timeline or default to pending
                    const currentStatus = "pending"; // This would come from the timeline data

                    const statusStyle = getStatusColor(currentStatus);
                    return (
                      <div
                        key={application.id}
                        className="bg-white rounded-xl border border-gray-100 drop-shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                        onClick={() => handleApplicationClick(application)}
                      >
                        {/* Vehicle Image */}
                        {application.vehicleInfo && (
                          <div className="h-32 bg-gray-100 relative overflow-hidden">
                            <Image
                              src={application.vehicleInfo.image}
                              alt={`${application.vehicleInfo.make} ${application.vehicleInfo.model}`}
                              fill
                              className="object-contain"
                            />
                            <div className="absolute top-3 right-3 bg-[#009639] px-2 py-1 rounded-full">
                              <span className="text-xs font-medium text-white">
                                {application.type.charAt(0).toUpperCase() +
                                  application.type.slice(1)}
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="p-4">
                          <div className="flex justify-between items-start gap-3 mb-3">
                            <div className="flex-1 min-w-0">
                              <h3 className="text-lg font-bold text-[#333333] mb-1">
                                {getApplicationTitle()}
                              </h3>
                              <p className="text-[#797879] text-sm">
                                {getApplicationSubtitle()}
                              </p>
                            </div>
                            <span
                              className={`text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${statusStyle.bg} ${statusStyle.border} ${statusStyle.text}`}
                              style={{ borderWidth: "1px" }}
                            >
                              {getStatusText(currentStatus)}
                            </span>
                          </div>

                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm">
                              <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                <span className="text-[#009639] font-bold text-xs">
                                  #
                                </span>
                              </div>
                              <span className="text-[#797879]">ID: </span>
                              <span className="text-[#333333] font-medium">
                                {application.id}
                              </span>
                            </div>
                            <div className="flex items-center text-sm">
                              <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                <Calendar
                                  size={10}
                                  className="text-[#009639]"
                                />
                              </div>
                              <span className="text-[#797879]">
                                Submitted:{" "}
                              </span>
                              <span className="text-[#333333] font-medium">
                                {formatDate(application.submittedAt)}
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-sm text-[#009639]">
                              <Clock size={16} className="mr-1" />
                              <span>View Status Timeline</span>
                            </div>
                            <ChevronRight
                              size={20}
                              className="text-[#797879]"
                            />
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Application Status Sheet */}
      <ApplicationStatusSheet
        isOpen={showStatusSheet}
        onClose={() => setShowStatusSheet(false)}
        application={selectedApplication}
      />
    </>
  );
}
