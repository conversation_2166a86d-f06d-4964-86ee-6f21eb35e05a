import { pgTable, index, unique, serial, varchar, text, boolean, timestamp, foreignKey, integer, numeric, doublePrecision, json, date, primaryKey, pgEnum, uuid, decimal, jsonb, check, pgView, pgMaterializedView} from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"
import { AudienceEnum, ListingTypeEnum } from "@/types/listings"
import { CompanyPurposeEnum } from "@/types/company"
import { GroupRoleEnum, InvitationStatusEnum } from "@/types/groups"
import { PartyStatusEnum } from "@/types/party"
import { ConditionLevel } from "@/types/bookings"

export const partyStatusEnum = pgEnum("party_status_enum", PartyStatusEnum)
export const audienceenum = pgEnum("audienceenum", AudienceEnum)
export const companyPurposeEnum = pgEnum("company_purpose", CompanyPurposeEnum)
export const bookingstatus = pgEnum("bookingstatus", ['PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED'])
export const cleanlinesslevel = pgEnum("cleanlinesslevel", ['clean', 'acceptable', 'dirty'])
export const commentpriority = pgEnum("commentpriority", ['LOW', 'MEDIUM', 'HIGH'])
export const commentstatus = pgEnum("commentstatus", ['OPEN', 'RESOLVED', 'DELETED'])
export const companyownershipinviteenum = pgEnum("companyownershipinviteenum", ['SENT', 'DECLINED', 'ACCEPTED'])
export const companytypeenum = pgEnum("companytypeenum", ['PRIVATE_COMPANY', 'NON_PROFIT', 'PARTNERSHIP', 'COOPERATIVE'])

export const conditionenum = pgEnum("conditionenum", ['new', 'used'])
export const conditionlevel = pgEnum("conditionlevel", ConditionLevel)
export const dashboardcondition = pgEnum("dashboardcondition", ['working', 'partial', 'issues'])
export const disputestatus = pgEnum("disputestatus", ['OPEN', 'RESOLVED', 'DELETED'])
export const disputetype = pgEnum("disputetype", ['BOOKING', 'vEEHICLE_DAMAGE', 'VEHICLE_MAINTENANCE', 'MAINTENANCE_COST_DISPUTE', 'OTHER'])
export const documenttype = pgEnum("documenttype", ['registration', 'insurance', 'inspection', 'other'])
export const generalcondition = pgEnum("generalcondition", ['good', 'fair', 'poor'])
export const lightscondition = pgEnum("lightscondition", ['working', 'partial', 'broken'])
export const listingtypeenum = pgEnum("listingtypeenum", ListingTypeEnum)
export const odorlevel = pgEnum("odorlevel", ['none', 'mild', 'strong'])
export const phototype = pgEnum("phototype", ['left_view', 'right_view', 'rear_view', 'front_view', 'dashboard', 'seats_view', 'interior', 'additional', 'tires', 'signature'])
export const possessionstatus = pgEnum("possessionstatus", ['PENDING', 'COMPLETED', 'CANCELLED'])
export const priority = pgEnum("priority", ['LOW', 'MEDIUM', 'HIGH'])
export const referencetypeenum = pgEnum("referencetypeenum", ['TAX_PIN', 'URL'])
export const servicestatus = pgEnum("servicestatus", ['SCHEDULED', 'PENDING', 'COMPLETED'])
export const servicetypeenum = pgEnum("servicetypeenum", ['MAINTENANCE', 'INSURANCE', 'CLEANING', 'FUEL', 'OTHER'])
export const vehicleservicestatus = pgEnum("vehicleservicestatus", ['SCHEDULED', 'PENDING', 'COMPLETED'])
export const verificationtypeenum = pgEnum("verificationtypeenum", ['AI', 'MANUAL', 'API'])
export const fuelTypeEnum = pgEnum('fuel_type', ['petrol', 'diesel', 'electric', 'hybrid', 'gas', 'other'])
export const transmissionEnum = pgEnum('transmission', ['automatic', 'manual', 'cvt', 'dual_clutch', 'other'])
export const drivetrainEnum = pgEnum('drivetrain', ['fwd', 'rwd', 'awd', '4wd', 'other'])
export const bodyTypeEnum = pgEnum('body_type', [
  'sedan', 'hatchback', 'suv', 'truck', 'coupe', 'convertible', 'wagon', 'van', 'minivan', 'other'
])

// Group role enum


export const groupRoleEnum = pgEnum('group_role', GroupRoleEnum)

// Group related enums
export const purpose = pgEnum('group_purpose', ['RIDE_SHARE', 'FLEET_MANAGEMENT', 'CO_OWNERSHIP', 'SOCIAL', 'OTHER'])

// Keep existing vehicle fuel and transmission enums for backward compatibility
export const vehicleFuelType = pgEnum("vehicle_fuel_type", ["petrol", "diesel", "electric", "hybrid", "plug-in-hybrid"])
export const vehicleTransmission = pgEnum("vehicle_transmission", ["manual", "automatic", "semi-automatic"])

// Task type and status enums
export const taskTypeEnum = pgEnum('task_type', [
  'GROUP_INVITATION',
  'COMPLIANCE',
  'ONBOARDING',
  'MAINTENANCE',
  'FINANCIAL',
  'DOCUMENT_UPLOAD',
  'APPROVAL',
  'BOOKING_ACKNOWLEDGMENT',
  'VEHICLE_HANDOVER_GIVER',
  'VEHICLE_HANDOVER_RECEIVER'
]);

export const taskStatusEnum = pgEnum('task_status', [
  'PENDING',
  'IN_PROGRESS', 
  'COMPLETED',
  'DISMISSED',
  'EXPIRED'
]);

export const taskPriorityEnum = pgEnum('task_priority', [
  'BLOCKING',
  'URGENT',
  'NORMAL',
  'OPTIONAL'
]);

export const provinceState = pgTable("province_state", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	countryId: integer().notNull().references(() => countries.id),
}, (table) => [
	unique("uq_province_or_state_name_country").on(table.name, table.countryId),
]);

export const cities = pgTable("cities", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	countryId: integer("country_id").notNull().references(()=>countries.id),
	provinceStateId: integer("province_state_id").notNull().references(()=>provinceState.id)
	
}, (table) => [
	index("ix_cities_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),

]);

export const accountType = pgTable("account_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("account_type_name_key").on(table.name),
]);

export const alembicVersion = pgTable("alembic_version", {
	versionNum: varchar("version_num", { length: 32 }).primaryKey().notNull(),
});

export const companyOwnership = pgTable("company_ownership", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	companyId: integer("company_id").notNull(),
	fraction: numeric().notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "company_ownership_company_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "company_ownership_party_id_fkey"
		}).onDelete("cascade"),
]);


export const addressType = pgTable("address_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("address_type_name_key").on(table.name),
]);

export const bookings = pgTable("bookings", {
	vehicleId: integer("vehicle_id").notNull(),
	reference: varchar({ length: 50 }).notNull(),
	startDatetime: timestamp("start_datetime", { mode: 'string' }).notNull(),
	endDatetime: timestamp("end_datetime", { mode: 'string' }).notNull(),
	status: bookingstatus(),
	totalPrice: doublePrecision("total_price"),
	notes: text(),
	partyId: integer("party_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_bookings_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "bookings_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "bookings_vehicle_id_fkey"
		}),
	unique("bookings_reference_key").on(table.reference),
]);


export const contactType = pgTable("contact_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contact_type_name_key").on(table.name),
]);

export const contractStatus = pgTable("contract_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contract_status_name_key").on(table.name),
]);

export const contactPoint = pgTable("contact_point", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	contactPointTypeId: integer("contact_point_type_id").notNull(),
	value: text().notNull(),
	addressTypeId: integer("address_type_id"),
	isPrimary: boolean("is_primary").notNull(),
	isVerified: boolean("is_verified").notNull(),
	verificationDate: timestamp("verification_date", { mode: 'string' }),
	verificationMethod: text("verification_method"),
	mtadata: json(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.addressTypeId],
			foreignColumns: [addressType.id],
			name: "contact_point_address_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.contactPointTypeId],
			foreignColumns: [contactPointType.id],
			name: "contact_point_contact_point_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "contact_point_party_id_fkey"
		}).onDelete("cascade"),
]);

export const contactPointType = pgTable("contact_point_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	validationPattern: text("validation_pattern"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contact_point_type_name_key").on(table.name),
]);

export const contractType = pgTable("contract_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("contract_type_name_key").on(table.name),
]);

export const identificationType = pgTable("identification_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	validationPattern: text("validation_pattern"),
	expirationRequired: boolean("expiration_required"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("identification_type_name_key").on(table.name),
]);

export const disputeMedia = pgTable("dispute_media", {
	disputeId: integer("dispute_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_dispute_media_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.disputeId],
			foreignColumns: [disputes.id],
			name: "dispute_media_dispute_id_fkey"
		}),
]);

export const documentType = pgTable("document_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("document_type_name_key").on(table.name),
]);

export const emergencyContacts = pgTable("emergency_contacts", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	phoneNumber: varchar("phone_number").notNull(),
	contactType: varchar("contact_type").notNull(),
	description: varchar(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	index("ix_emergency_contacts_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
]);

export const individual = pgTable("individual", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	firstName: text("first_name").notNull(),
	lastName: text("last_name").notNull(),
	middleName: text("middle_name"),
	salutation: text(),
	suffix: text(),
	gender: text(),
	birthDate: timestamp("birth_date", { mode: 'string' }),
	maritalStatus: text("marital_status"),
	nationality: text(),
	preferredLanguage: text("preferred_language"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "individual_party_id_fkey"
		}).onDelete("cascade"),
]);

export const disputes = pgTable("disputes", {
	name: varchar().notNull(),
	description: text(),
	vehicleId: integer("vehicle_id").notNull(),
	disputeType: disputetype("dispute_type").notNull(),
	partyOffending: integer("party_offending").notNull(),
	partyLogging: integer("party_logging").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	priority: priority(),
	companyId: integer("company_id").notNull(),
	disputeStatus: disputestatus("dispute_status"),
}, (table) => [
	index("ix_disputes_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.partyOffending],
			foreignColumns: [party.id],
			name: "disputes_party_offending_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyLogging],
			foreignColumns: [party.id],
			name: "disputes_party_logging_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "disputes_company_id_fkey"
		}),
]);

export const industry = pgTable("industry", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("industry_name_key").on(table.name),
]);

export const lead = pgTable("lead", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	company: text(),
	title: text(),
	statusId: integer("status_id").notNull(),
	sourceId: integer("source_id"),
	rating: text(),
	annualRevenue: integer("annual_revenue"),
	numberOfEmployees: integer("number_of_employees"),
	industry: text(),
	description: text(),
	isConverted: boolean("is_converted").notNull(),
	convertedDate: timestamp("converted_date", { mode: 'string' }),
	convertedAccountId: integer("converted_account_id"),
	convertedContactId: integer("converted_contact_id"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "lead_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.sourceId],
			foreignColumns: [leadSource.id],
			name: "lead_source_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.statusId],
			foreignColumns: [leadStatus.id],
			name: "lead_status_id_fkey"
		}).onDelete("cascade"),
]);

export const leadSource = pgTable("lead_source", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("lead_source_name_key").on(table.name),
]);

export const leadStatus = pgTable("lead_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("lead_status_name_key").on(table.name),
]);

export const matchRuleType = pgTable("match_rule_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	confidence: integer(),
	isActive: boolean("is_active"),
	priority: integer(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const opportunityStage = pgTable("opportunity_stage", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	probability: integer(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("opportunity_stage_name_key").on(table.name),
]);

export const opportunityType = pgTable("opportunity_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("opportunity_type_name_key").on(table.name),
]);

export const issuingAuthority = pgTable("issuing_authority", {
	partyId: integer("party_id").notNull(),
	name: varchar().notNull(),
	description: varchar(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	country: varchar(),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "issuing_authority_party_id_fkey"
		}).onDelete("cascade"),
	unique("issuing_authority_name_key").on(table.name),
]);

export const partyStatus = pgTable("party_status", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull().references(() => party.id),
	status: partyStatusEnum("status").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "party_status_party_id_fkey"
		}).onDelete("cascade"),
]);

export const party = pgTable("party", {
	id: serial().primaryKey().notNull(),
	partyTypeId: integer("party_type_id").notNull(),
	externalId: text("external_id"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyTypeId],
			foreignColumns: [partyType.id],
			name: "party_party_type_id_fkey"
		}).onDelete("cascade")
]);

export const partyIdentification = pgTable("party_identification", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	identificationTypeId: integer("identification_type_id").notNull(),
	documentNumber: text("document_number").notNull(),
	issuingAuthority: text("issuing_authority"),
	issueDate: timestamp("issue_date", { mode: 'string' }),
	expiryDate: timestamp("expiry_date", { mode: 'string' }),
	isVerified: boolean("is_verified").notNull(),
	verificationDate: timestamp("verification_date", { mode: 'string' }),
	verificationMethod: text("verification_method"),
	documentImageUrl: text("document_image_url"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.identificationTypeId],
			foreignColumns: [identificationType.id],
			name: "party_identification_identification_type_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "party_identification_party_id_fkey"
		}).onDelete("cascade"),
]);

export const productType = pgTable("product_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("product_type_name_key").on(table.name),
]);

export const recordType = pgTable("record_type", {
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("record_type_name_key").on(table.name),
]);

export const relationshipType = pgTable("relationship_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("relationship_type_name_key").on(table.name),
]);

export const vehicleInspections = pgTable("vehicle_inspections", {
	scratches: conditionlevel().notNull(),
	dents: conditionlevel().notNull(),
	tires: generalcondition().notNull(),
	lights: lightscondition().notNull(),
	cleanliness: cleanlinesslevel().notNull(),
	seats: generalcondition().notNull(),
	dashboardControls: dashboardcondition("dashboard_controls").notNull(),
	odors: odorlevel().notNull(),
	odometer: integer().notNull(),
	knownIssues: text("known_issues"),
	vehicleId: integer("vehicle_id").notNull(),
	possessionId: integer("possession_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_inspections_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.possessionId],
			foreignColumns: [vehiclePossessions.id],
			name: "vehicle_inspections_possession_id_fkey"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_inspections_vehicle_id_fkey"
		}).onDelete("cascade"),
]);

export const socialMediaType = pgTable("social_media_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	baseUrl: text("base_url"),
	icon: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("social_media_type_name_key").on(table.name),
]);

export const subscriptionStatus = pgTable("subscription_status", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	displayOrder: integer("display_order"),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	unique("subscription_status_name_key").on(table.name),
]);

export const users = pgTable("users", {
	id: serial().primaryKey().notNull(),
	username: varchar({ length: 50 }).notNull(),
	email: varchar({ length: 100 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	familyName: varchar("family_name", { length: 100 }),
	givenName: varchar("given_name", { length: 100 }),
	phoneNumber: varchar("phone_number", { length: 20 }),
	orgId: varchar("org_id", { length: 16 }),
}, (table) => [
	unique("users_username_key").on(table.username),
	unique("users_email_key").on(table.email),
]);

export const vehicleMaintenance = pgTable("vehicle_maintenance", {
	vehicleId: integer("vehicle_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	description: varchar({ length: 500 }),
	dueDate: timestamp("due_date", { mode: 'string' }).notNull(),
	dueOdometer: doublePrecision("due_odometer").notNull(),
	status: vehicleservicestatus().notNull(),
	expectedCost: doublePrecision("expected_cost").notNull(),
	completedDate: timestamp("completed_date", { mode: 'string' }),
	completedOdometer: doublePrecision("completed_odometer"),
	actualCost: doublePrecision("actual_cost"),
	technicianNotes: varchar("technician_notes", { length: 1000 }),
	serviceProvider: varchar("service_provider", { length: 1000 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	isScheduled: boolean("is_scheduled"),
	id: serial().notNull(),
}, (table) => [
	index("ix_vehicle_maintenance_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_maintenance_vehicle_id_fkey"
		}),
]);

export const vehicleMake = pgTable("vehicle_make", {
	name: varchar().notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_make_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
]);

export const vehicleVariant = pgTable("vehicle_variant", {
	id: serial().primaryKey().notNull(),
	modelId: integer("model_id"),
	name: varchar().notNull(),
	trimName: text("trim_name"),
	year: integer().notNull(),
	engine: text(),
	drivetrain: drivetrainEnum(),
	bodyType: bodyTypeEnum("body_type"),
	seats: integer(),
	doors: integer(),
	msrp: decimal({ precision: 10, scale: 2 }),
	features: jsonb(),
	specs: jsonb(),
	fuelType: fuelTypeEnum("fuel_type").notNull(),
	transmission: transmissionEnum("transmission").notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_variant_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	index("ix_vehicle_variant_name_key").using("btree", table.name.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.modelId],
			foreignColumns: [vehicleModel.id],
			name: "vehicle_variant_model_id_fkey"
		}).onDelete("cascade"),
]);

export const partyType = pgTable("party_type", {
	id: serial().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	
}, (table) => [
	unique("party_type_name_key").on(table.name),
]);

export const socialProfile = pgTable("social_profile", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	socialMediaTypeId: integer("social_media_type_id").notNull(),
	username: text().notNull(),
	url: text(),
	isPrimary: boolean("is_primary").notNull(),
	isVerified: boolean("is_verified").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "social_profile_party_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.socialMediaTypeId],
			foreignColumns: [socialMediaType.id],
			name: "social_profile_social_media_type_id_fkey"
		}).onDelete("cascade"),
]);

export const vehicleModelMedia = pgTable("vehicle_model_media", {
	vehicleModelId: integer("vehicle_model_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_model_media_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleModelId],
			foreignColumns: [vehicleModel.id],
			name: "vehicle_model_media_vehicle_model_id_fkey"
		}),
]);

export const vehiclePhotos = pgTable("vehicle_photos", {
	inspectionId: integer("inspection_id").notNull(),
	type: phototype().notNull(),
	fileUrl: varchar("file_url").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_photos_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.inspectionId],
			foreignColumns: [vehicleInspections.id],
			name: "vehicle_photos_inspection_id_fkey"
		}).onDelete("cascade"),
]);

export const verification = pgTable("verification", {
	verifyingPartyId: integer("verifying_party_id").notNull(),
	verificationOutcome: varchar("verification_outcome"),
	cost: doublePrecision(),
	verificationType: verificationtypeenum("verification_type"),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.verifyingPartyId],
			foreignColumns: [party.id],
			name: "verification_verifying_party_id_fkey"
		}),
]);

export const vehiclePossessions = pgTable("vehicle_possessions", {
	fromPartyId: integer("from_party_id").notNull(),
	toPartyId: integer("to_party_id").notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	handoverExpectedDatetime: timestamp("handover_expected_datetime", { mode: 'string' }),
	handoverActualDatetime: timestamp("handover_actual_datetime", { mode: 'string' }),
	status: possessionstatus().notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_possessions_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.fromPartyId],
			foreignColumns: [party.id],
			name: "vehicle_possessions_from_party_id_fkey"
		}),
	foreignKey({
			columns: [table.toPartyId],
			foreignColumns: [party.id],
			name: "vehicle_possessions_to_party_id_fkey"
		}),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_possessions_vehicle_id_fkey"
		}),
]);

export const vehicleMedia = pgTable("vehicle_media", {
	vehicleId: integer("vehicle_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_media_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_media_vehicle_id_fkey"
		}),
]);

export const vehicleModel = pgTable("vehicle_model", {
	makeId: integer("make_id").notNull(),
	model: varchar().notNull(),
	slug: text(),
	firstYear: integer("first_year"),
	lastYear: integer("last_year"),
	bodyType: bodyTypeEnum("body_type"),
	description: text(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_model_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.makeId],
			foreignColumns: [vehicleMake.id],
			name: "vehicle_model_make_id_fkey"
		}).onDelete("cascade"),
]);

export const countries = pgTable("countries", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	code: varchar().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

export const vehicles = pgTable("vehicles", {
	partyId: integer("party_id").notNull(),
	modelId: integer("model_id").notNull(),
	vinNumber: varchar("vin_number").notNull(),
	vehicleRegistration: varchar("vehicle_registration"),
	countryId: integer("country_id").references(() => countries.id).default(1),
	manufacturingYear: integer("manufacturing_year"),
	purchaseDate: timestamp("purchase_date", { withTimezone: true, mode: 'string' }),
	color: varchar(),
	isActive: boolean("is_active").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicles_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.modelId],
			foreignColumns: [vehicleModel.id],
			name: "vehicles_model_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "vehicles_party_id_fkey"
		}).onDelete("cascade"),
	unique("vehicles_vin_number_key").on(table.vinNumber),
]);

export const company = pgTable("company", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	registrationNumber: varchar("registration_number"),
	countryId: integer("country_id").references(() => countries.id).default(1),
	registrationDate: timestamp("registration_date", { withTimezone: true, mode: 'string' }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	name: varchar(),
	description: varchar(),
	cityId: integer("city_id"),
	purpose: varchar(),
}, (table) => [
	foreignKey({
			columns: [table.cityId],
			foreignColumns: [cities.id],
			name: "company_city_id_fkey"
		}),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "company_party_id_fkey"
		}).onDelete("cascade"),
]);

export const disputeComments = pgTable("dispute_comments", {
	comment: text().notNull(),
	disputeId: integer("dispute_id").notNull(),
	replyToCommentId: integer("reply_to_comment_id"),
	userId: integer("user_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_dispute_comments_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.disputeId],
			foreignColumns: [disputes.id],
			name: "dispute_comments_dispute_id_fkey"
		}),
	foreignKey({
			columns: [table.replyToCommentId],
			foreignColumns: [table.id],
			name: "dispute_comments_reply_to_comment_id_fkey"
		}),
]);

export const organization = pgTable("organization", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").notNull(),
	name: text().notNull(),
	description: text(),
	industry: text(),
	numberOfEmployees: integer("number_of_employees"),
	annualRevenue: integer("annual_revenue"),
	websiteUrl: text("website_url"),
	logoUrl: text("logo_url"),
	isDeleted: boolean("is_deleted").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "organization_party_id_fkey"
		}).onDelete("cascade"),
]);

export const vehicleDocuments = pgTable("vehicle_documents", {
	id: serial().primaryKey().notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	documentType: documenttype("document_type").notNull(),
	expirationDate: timestamp("expiration_date", { withTimezone: true, mode: 'string' }),
	name: varchar(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_vehicle_documents_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "vehicle_documents_vehicle_id_fkey"
		}),
]);

export const listingMedia = pgTable("listing_media", {
	listingId: integer("listing_id").notNull(),
	mediaPath: varchar("media_path").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.listingId],
			foreignColumns: [listings.id],
			name: "listing_media_listing_id_fkey"
		}),
]);

export const companyOwnershipInvite = pgTable("company_ownership_invite", {
	id: serial().primaryKey().notNull(),
	companyId: integer("company_id").notNull(),
	fraction: numeric().notNull(),
	status: companyownershipinviteenum().notNull(),
	email: varchar({ length: 100 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	firstName: text("first_name"),
	lastName: text("last_name"),
}, (table) => [
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "company_ownership_invite_company_id_fkey"
		}).onDelete("cascade"),
]);


export const serviceProviders = pgTable("service_providers", {
	name: varchar().notNull(),
	description: varchar(),
	serviceType: servicetypeenum("service_type").notNull(),
	effectiveFrom: date("effective_from").notNull(),
	effectiveTo: date("effective_to").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const listings = pgTable("listings", {
	partyId: integer("party_id").notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }).default(sql`'infinity'::timestamp`),
	fraction: doublePrecision("fraction").notNull(),
	askingPrice: doublePrecision("asking_price").notNull(),
	condition: conditionenum().notNull(),
	mileage: doublePrecision(),
	listingType: listingtypeenum("listing_type").notNull(),
	audience: audienceenum().notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "listings_party_id_fkey"
		}),
	foreignKey({
			columns: [table.vehicleId],
			foreignColumns: [vehicles.id],
			name: "listings_vehicle_id_fkey"
		}),
]);

export const opportunities = pgTable("opportunities", {
	opportunityName: varchar("opportunity_name").notNull(),
	description: varchar(),
	companyId: integer("company_id").notNull(),
	price: doublePrecision().notNull(),
	fraction: doublePrecision().notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "opportunities_company_id_fkey"
		}),
]);

export const listingInterestExpressions = pgTable("listing_interest_expressions", {
	listingId: integer("listing_id").notNull(),
	partyId: integer("party_id").notNull(),
	id: serial().primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.listingId],
			foreignColumns: [listings.id],
			name: "listing_interest_expressions_listing_id_fkey"
		}),
	foreignKey({
			columns: [table.partyId],
			foreignColumns: [party.id],
			name: "listing_interest_expressions_party_id_fkey"
		}),
]);

export const votingThreshold = pgTable("voting_threshold", {
	id: integer().notNull(),
	companyId: integer("company_id").notNull(),
	unanimous: boolean(),
	simpleMajority: boolean("simple_majority"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_voting_threshold_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	primaryKey({ columns: [table.id, table.companyId], name: "voting_threshold_pkey"}),
]);

export const companyNotificationPreferences = pgTable("company_notification_preferences", {
	id: integer().notNull(),
	companyId: integer("company_id").notNull(),
	bookingNotifications: boolean("booking_notifications"),
	paymentNotifications: boolean("payment_notifications"),
	maintenanceAlerts: boolean("maintenance_alerts"),
	memberActivity: boolean("member_activity"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("ix_company_notification_preferences_id").using("btree", table.id.asc().nullsLast().op("int4_ops")),
	foreignKey({
			columns: [table.companyId],
			foreignColumns: [company.id],
			name: "company_notification_preferences_company_id_fkey"
		}),
	primaryKey({ columns: [table.id, table.companyId], name: "company_notification_preferences_pkey"}),
]);

export const groups = pgTable("groups", {
	id: serial().primaryKey().notNull(),
	partyId: integer("party_id").references(() => party.id).notNull(),
	name: varchar().notNull(),
	description: text(),
	cityId: integer("city_id").references(() => cities.id),
	countryId: integer("country_id").references(() => countries.id),
	initialPurpose: companyPurposeEnum("initial_purpose").notNull(),
	isManaged: boolean("is_managed").default(false).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	createdBy: integer("created_by").notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	updatedBy: integer("updated_by"),
	creator: integer("creator").references(() => party.id).notNull(),
});

export const groupMemberships = pgTable("group_memberships", {
	id: serial().primaryKey().notNull(),
	groupId: integer("group_id").references(() => groups.id).notNull(),
	partyId: integer("party_id").references(() => party.id).notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }).default(sql`'infinity'::timestamp`),
});

export const groupMemberRoles = pgTable("group_member_roles", {
	id: serial().primaryKey().notNull(),
	groupId: integer("group_id").references(() => groups.id).notNull(),
	partyId: integer("party_id").references(() => party.id).notNull(),
	role: groupRoleEnum("role").notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }).default(sql`'infinity'::timestamp`),

});

// Group membership invitation status enum
export const invitationStatusEnum = pgEnum('invitation_status', InvitationStatusEnum);

// Group membership invitations table
export const groupMembershipInvitations = pgTable("group_membership_invitations", {
	id: serial().primaryKey().notNull(),
	groupId: integer("group_id").references(() => groups.id).notNull(),
	firstName: varchar("first_name").notNull(),
	lastName: varchar("last_name").notNull(),
	email: varchar("email").notNull(),
	role: groupRoleEnum("role").notNull(),
	status: invitationStatusEnum("status").default(InvitationStatusEnum.SENT).notNull(),
	invitedBy: integer("invited_by").references(() => party.id).notNull(),
	invitationToken: varchar("invitation_token").notNull(),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }).notNull(),
	acceptedAt: timestamp("accepted_at", { withTimezone: true, mode: 'string' }),
	acceptedBy: integer("accepted_by").references(() => party.id),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
});

// Group shared vehicles table
export const groupSharedVehicles = pgTable("group_shared_vehicles", {
	id: serial().primaryKey().notNull(),
	groupId: integer("group_id").references(() => groups.id).notNull(),
	vehicleId: integer("vehicle_id").references(() => vehicles.id).notNull(),
	sharedBy: integer("shared_by").references(() => party.id).notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }).default(sql`'infinity'::timestamp`),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	unique("unique_group_vehicle").on(table.groupId, table.vehicleId),
]);

// Tasks table
export const tasks = pgTable("tasks", {
	id: serial().primaryKey().notNull(),
	type: taskTypeEnum("type").notNull(),
	status: taskStatusEnum("status").default('PENDING').notNull(),
	priority: taskPriorityEnum("priority").default('NORMAL').notNull(),
	title: varchar("title").notNull(),
	description: text("description"),
	email: varchar("email").notNull(),
	partyId: integer("party_id").references(() => party.id),
	relatedEntityId: integer("related_entity_id"), // Can reference invitation ID, maintenance ID, etc.
	metadata: jsonb("metadata"), // Store task-specific data as JSON
	estimatedMinutes: integer("estimated_minutes"),
	expiresAt: timestamp("expires_at", { withTimezone: true, mode: 'string' }),
	completedAt: timestamp("completed_at", { withTimezone: true, mode: 'string' }),
	completedBy: integer("completed_by").references(() => party.id),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	index("idx_tasks_email").on(table.email),
	index("idx_tasks_party_id").on(table.partyId),
	index("idx_tasks_status").on(table.status),
	index("idx_tasks_type").on(table.type),
]);

export const asset = pgTable("assets", {
	id: serial().primaryKey().notNull(),
	name: varchar().notNull(),
	description: text(),
	status: varchar().notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true }),
	createdBy: integer("created_by").references(() => party.id),
	updatedBy: integer("updated_by").references(() => party.id),
});

export const assetOwnership = pgTable("asset_ownership", {
	id: serial().primaryKey().notNull(),
	assetId: integer("asset_id").references(() => asset.id).notNull(),
	partyId: integer("party_id").references(() => party.id).notNull(),
	fraction: numeric("fraction").notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true }).defaultNow().notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true }),
});

export const applicationStatusEnum = pgEnum("application_status", [
	'DRAFT',
	'SUBMITTED',
	'UNDER_REVIEW',
	'APPROVED',
	'REJECTED',
	'CANCELLED'
]);

export const applicationTypeEnum = pgEnum("application_type", [
	'CO_OWNERSHIP',
	'LEASE',
]);

export const applications = pgTable("applications", {
	id: serial().primaryKey().notNull(),
	type: applicationTypeEnum("type").notNull(),
	status: applicationStatusEnum("status").notNull(),
	applicantId: integer("applicant_id").references(() => party.id).notNull(),
	assetId: integer("asset_id").references(() => asset.id).notNull(),
	fraction: numeric("fraction"),
	amount: decimal("amount", { precision: 10, scale: 2 }),
	duration: integer(), // in months for rental/lease
	startDate: timestamp("start_date", { withTimezone: true }),
	endDate: timestamp("end_date", { withTimezone: true }),
	notes: text(),
	reviewerId: integer("reviewer_id").references(() => party.id),
	reviewNotes: text("review_notes"),
	reviewedAt: timestamp("reviewed_at", { withTimezone: true }),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true }),
});

export const applicationDocuments = pgTable("application_documents", {
	id: serial().primaryKey().notNull(),
	applicationId: integer("application_id").references(() => applications.id).notNull(),
	documentType: varchar().notNull(),
	documentUrl: varchar().notNull(),
	uploadedAt: timestamp("uploaded_at", { withTimezone: true }).defaultNow().notNull(),
	verificationStatus: varchar(),
	verifiedAt: timestamp("verified_at", { withTimezone: true }),
	verifiedBy: integer("verified_by").references(() => party.id),
});

// Base offer types with their specific requirements
export const offerTypeEnum = pgEnum('offer_type', [
    'CO_OWNERSHIP',    // Requires fraction, no duration (infinite)
    'LEASE',          // No fraction (always 100%), requires duration
    'RENTAL',         // No fraction (always 100%), requires duration
    'FULL_SALE'       // No fraction (always 100%), no duration
]);

// Duration types
export const durationTypeEnum = pgEnum('duration_type', [
    'TERM',           // Specific time period with end date
    'INFINITE'        // No end date (for ownership or open-ended arrangements)
]);

export const offerStatusEnum = pgEnum('offer_status', [
    'DRAFT',
    'ACTIVE',
    'PAUSED',
    'EXPIRED',
    'WITHDRAWN'
]);

export const financeMethodEnum = pgEnum('finance_method', [
    'CASH',
    'FINANCE',
    'CASH_AND_FINANCE'
]);

export const listingOffers = pgTable("listing_offers", {
    id: serial().primaryKey().notNull(),
    listingId: integer("listing_id").references(() => listings.id).notNull(),
    offerType: offerTypeEnum("offer_type").notNull(),
    
    // Ownership-specific fields
    fraction: decimal("fraction", { precision: 5, scale: 2 }), // NULL for non-fractional offers
    
    // Time-based fields
    durationType: durationTypeEnum("duration_type").notNull(),
    // Common fields
    price: decimal("price", { precision: 10, scale: 2 }).notNull(),
    financeMethod: financeMethodEnum("finance_method").notNull(),
    financingTerms: jsonb("financing_terms"), // Optional, populated when FINANCE or CASH_AND_FINANCE
    terms: jsonb("terms").notNull(),
    status: offerStatusEnum("status").notNull(),
    // Duration (the time period)
    validFrom: timestamp("valid_from", { withTimezone: true }).notNull(),
    validTo: timestamp("valid_to", { withTimezone: true }).default(sql`'infinity'::timestamp`),
    
    // Terms (the conditions)
    contractTerms: jsonb("contract_terms").notNull(), // Rules, conditions, obligations
    paymentTerms: jsonb("payment_terms").notNull(),   // Payment schedules, methods
    usageTerms: jsonb("usage_terms"),                 // How the asset can be used

    // Constraints
   
});

export const riskLevelEnum = pgEnum('risk_level', ['LOW', 'MEDIUM', 'HIGH']);

export const requirementTypeEnum = pgEnum('requirement_type', [
    'DOCUMENT',
	'CERTIFIED_DOCUMENT',
	'IMAGE',
	'VIDEO',
	'AUDIO',
    'CERTIFICATION',
    'ATTESTATION',
    'INSPECTION',
    'AUDIT',
	'SITE_INSPECTION',
	'VEHICLE_INSPECTION',
	'OTP',
	'OTHER',
]);

export const requirementStatusEnum = pgEnum('requirement_status', [
    'ACTIVE',
    'INACTIVE',
    'DEPRECATED'
]);

export const complianceCategoryEnum = pgEnum('compliance_category', [
    'IDENTITY',           // National ID, Passport
    'DRIVING',           // Driver's License, PrDP
    'FINANCIAL',         // Bank Statements, Income Proof
    'ADDRESS',           // Proof of Residence
    'VEHICLE',           // Registration, Insurance
    'BUSINESS',          // Business Registration, Tax
    'VERIFICATION',      // Selfies, Video Verification
    'OTHER'
]);

export const complianceRequirement = pgTable("compliance_requirement", {
    id: serial("id").primaryKey().notNull(),
    code: varchar("code").notNull(), // unique identifier
    version: integer("version").notNull().default(1),
    name: varchar("name").notNull(),
    description: text("description"),
    category: complianceCategoryEnum("category").notNull(), // Changed from categoryId
    type: requirementTypeEnum().notNull(),
    status: requirementStatusEnum().notNull(),
    validationRules: jsonb("validation_rules"), // JSON schema for validation
    documentSchema: jsonb("document_schema"), // required fields for documents
    defaultValidityDays: integer("default_validity_days"),
    riskLevel: riskLevelEnum().notNull().default('LOW'),
    isRecurring: boolean("is_recurring").default(false).notNull(),
    recurringPeriodDays: integer("recurring_period_days"),
    metadata: jsonb("metadata"),
    effectiveFrom: timestamp("effective_from", { withTimezone: true }).notNull(),
    effectiveTo: timestamp("effective_to", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
});

export const requirementDependency = pgTable("requirement_dependency", {
    id: serial().primaryKey().notNull(),
    requirementId: integer().references(() => complianceRequirement.id).notNull(),
    dependsOnId: integer().references(() => complianceRequirement.id).notNull(),
    condition: jsonb(), // Conditions under which dependency applies
    isRequired: boolean().default(true).notNull(),
    metadata: jsonb(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
});

export const submissionStatusEnum = pgEnum('submission_status', [
    'DRAFT',
    'SUBMITTED',
    'UNDER_REVIEW',
    'ADDITIONAL_INFO_REQUIRED',
    'VERIFIED',
    'REJECTED',
    'EXPIRED'
]);

export const complianceSubmission = pgTable("compliance_submission", {
    id: serial("id").primaryKey().notNull(),
    requirementId: integer("requirement_id").references(() => complianceRequirement.id).notNull(),
    entityType: varchar().notNull(),
    entityId: integer().notNull(),
    currentStatusHistoryId: integer().references(() => statusHistory.id),
    batchId: uuid(), // For batch processing
    data: jsonb(), // Submitted data
    validationResults: jsonb(),
    submittedBy: integer().references(() => party.id).notNull(),
    submittedAt: timestamp("submitted_at", { withTimezone: true }).defaultNow().notNull(),
    expiresAt: timestamp("expires_at", { withTimezone: true }),
    metadata: jsonb(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
}, (table) => ({
    entityIdx: index("submission_entity_idx").on(table.entityType, table.entityId),
    currentStatusIdx: index("current_status_compliance_submission_idx").on(table.currentStatusHistoryId)
}));

export const documentStatusEnum = pgEnum('document_status', [
    'PENDING',
    'VERIFIED',
    'REJECTED',
    'EXPIRED'
]);

export const complianceDocument = pgTable("compliance_document", {
    id: serial().primaryKey().notNull(),
    submissionId: integer().references(() => complianceSubmission.id).notNull(),
    documentNumber: varchar(),
    documentType: varchar().notNull(),
    issuingAuthorityId: integer().references(() => issuingAuthority.id),
    issueDate: timestamp("issue_date", { withTimezone: true }),
    expiryDate: timestamp("expiry_date", { withTimezone: true }),
    status: documentStatusEnum().notNull(),
    documentUrl: varchar(),
    metadata: jsonb(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
});

export const verificationMethodEnum = pgEnum('verification_method', [
    'MANUAL',
    'API',
    'AI',
    'BLOCKCHAIN'
]);

export const verificationStatus = pgEnum('verification_status', [
    'PENDING',
    'IN_PROGRESS',
    'COMPLETED',
    'FAILED'
]);

export const complianceVerification = pgTable("compliance_verification", {
    id: serial().primaryKey().notNull(),
    submissionId: integer().references(() => complianceSubmission.id).notNull(),
    method: verificationMethodEnum().notNull(),
    status: verificationStatus().notNull(),
    verifiedBy: integer().references(() => party.id),
    verificationData: jsonb(),
    result: jsonb(),
    cost: decimal("cost", { precision: 10, scale: 2 }),
    metadata: jsonb(),
    startedAt: timestamp("started_at", { withTimezone: true }),
    completedAt: timestamp("completed_at", { withTimezone: true }),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
});

export const complianceAudit = pgTable("compliance_audit", {
    id: serial().primaryKey().notNull(),
    entityType: varchar().notNull(), // 'SUBMISSION', 'DOCUMENT', 'VERIFICATION'
    entityId: integer().notNull(),
    action: varchar().notNull(),
    actorId: integer().references(() => party.id).notNull(),
    changes: jsonb(),
    metadata: jsonb(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull()
}, (table) => {
    return {
        entityIdx: index("audit_entity_idx").on(table.entityType, table.entityId),
        createdAtIdx: index("audit_created_at_idx").on(table.createdAt)
    };
});

// Define all possible statuses across all entities
export const statusEnum = pgEnum('status', [
    // Submission statuses
    'DRAFT',
    'SUBMITTED',
    'UNDER_REVIEW',
    'ADDITIONAL_INFO_REQUIRED',
    'VERIFIED',
    'REJECTED',
    'EXPIRED',
    // Document statuses
    'PENDING_UPLOAD',
    'UPLOADED',
    'PENDING_VERIFICATION',
    'VERIFICATION_FAILED',
    'DOCUMENT_REJECTED',
    'DOCUMENT_ACCEPTED',
    // Verification statuses
    'VERIFICATION_PENDING',
    'VERIFICATION_IN_PROGRESS',
    'VERIFICATION_COMPLETED',

]);

// Status transition rules
export const statusTransitionRules = pgTable("status_transition_rules", {
    id: serial().primaryKey().notNull(),
    entityType: varchar().notNull(), // 'SUBMISSION', 'DOCUMENT', 'VERIFICATION'
    fromStatus: statusEnum().notNull(),
    toStatus: statusEnum().notNull(),
    allowedRoles: jsonb().notNull(), // Array of role IDs that can perform this transition
    requiresApproval: boolean().default(false).notNull(),
    validationRules: jsonb(), // Any specific rules for this transition
    metadata: jsonb(),
    isActive: boolean().default(true).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
}, (table) => {
    return {
        transitionIdx: index("status_transition_idx").on(
            table.entityType, 
            table.fromStatus, 
            table.toStatus
        )
    };
});

// Status history for all entities
export const statusHistory = pgTable("status_history", {
    id: serial("id").primaryKey().notNull(),
    entityType: varchar("entity_type").notNull(), // 'SUBMISSION', 'DOCUMENT', 'VERIFICATION'
    entityId: integer("entity_id").notNull(),
    fromStatus: statusEnum("from_status").notNull(),
    toStatus: statusEnum("to_status").notNull(),
    transitionRuleId: integer("transition_rule_id").references(() => statusTransitionRules.id),
    changedBy: integer("changed_by").references(() => party.id).notNull(),
    reason: text("reason"),
    notes: text("notes"),
    metadata: jsonb("metadata"),
    effectiveFrom: timestamp("effective_from", { withTimezone: true }).defaultNow().notNull(),
    effectiveTo: timestamp("effective_to", { withTimezone: true }), // NULL means currently active
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
}, (table) => ({
        entityIdx: index("status_history_entity_idx").on(
            table.entityType, 
            table.entityId
        ),
        currentStatusIdx: index("current_status_history_idx").on(
            table.entityType, 
            table.entityId, 
            table.effectiveTo
        )
}));

// Helper view for current status (materialized view for performance)
export const currentEntityStatus = pgTable("current_entity_status", {
    entityType: varchar().notNull(),
    entityId: integer().notNull(),
    currentStatus: statusEnum().notNull(),
    lastTransitionAt: timestamp("last_transition_at", { withTimezone: true }).notNull(),
    lastTransitionBy: integer().references(() => party.id).notNull(),
    statusHistoryId: integer().references(() => statusHistory.id).notNull()
}, (table) => {
    return {
        primaryKey: primaryKey({ columns: [table.entityType, table.entityId] })
    };
});

// Status change triggers/notifications
export const statusChangeNotification = pgTable("status_change_notification", {
    id: serial().primaryKey().notNull(),
    statusHistoryId: integer().references(() => statusHistory.id).notNull(),
    notificationType: varchar().notNull(), // email, SMS, in-app, etc.
    recipientId: integer().references(() => party.id).notNull(),
    content: jsonb(),
    sentAt: timestamp("sent_at", { withTimezone: true }),
    error: text(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull()
});

// Status-based SLA tracking
export const statusSLA = pgTable("status_sla", {
    id: serial().primaryKey().notNull(),
    entityType: varchar().notNull(),
    status: statusEnum().notNull(),
    expectedDurationHours: integer().notNull(),
    escalationRules: jsonb(),
    notificationRules: jsonb(),
    isActive: boolean().default(true).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
});

// SLA tracking
export const statusSLATracking = pgTable("status_sla_tracking", {
    id: serial().primaryKey().notNull(),
    statusHistoryId: integer().references(() => statusHistory.id).notNull(),
    slaId: integer().references(() => statusSLA.id).notNull(),
    dueAt: timestamp("due_at", { withTimezone: true }).notNull(),
    completedAt: timestamp("completed_at", { withTimezone: true }),
    isBreached: boolean().default(false).notNull(),
    escalationLevel: integer().default(0).notNull(),
    metadata: jsonb(),
    createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
});

// ================================
// VIEWS FOR GROUPS COMMUNITY PAGE
// ================================

// View to get group summary with member and vehicle counts - optimized for community page
export const groupsCommunityView = pgView("groups_community_view").as((qb) => {
  return qb
    .select({
      id: groups.id,
      name: groups.name,
      description: groups.description,
      createdAt: groups.createdAt,
      createdBy: groups.createdBy,
      cityName: sql`${cities.name}`.as('city_name'),
      countryName: sql`${countries.name}`.as('country_name'),
      isManaged: groups.isManaged,
      initialPurpose: groups.initialPurpose,
      memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`.as('member_count'),
      vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`.as('vehicle_count'),
    })
    .from(groups)
    .leftJoin(cities, sql`${groups.cityId} = ${cities.id}`)
    .leftJoin(countries, sql`${groups.countryId} = ${countries.id}`)
    .leftJoin(
      groupMemberships,
      sql`${groups.id} = ${groupMemberships.groupId} AND ${groupMemberships.effectiveFrom} <= NOW() AND (${groupMemberships.effectiveTo} IS NULL OR ${groupMemberships.effectiveTo} > NOW())`
    )
    .leftJoin(
      groupSharedVehicles,
      sql`${groups.id} = ${groupSharedVehicles.groupId} AND ${groupSharedVehicles.isActive} = true AND ${groupSharedVehicles.effectiveFrom} <= NOW() AND (${groupSharedVehicles.effectiveTo} IS NULL OR ${groupSharedVehicles.effectiveTo} > NOW())`
    )
    .groupBy(
      groups.id,
      groups.name,
      groups.description,
      groups.createdAt,
      groups.createdBy,
      cities.name,
      countries.name,
      groups.isManaged,
      groups.initialPurpose
    );
});

// View to get user's group memberships with roles for quick access
export const userGroupMembershipsView = pgView("user_group_memberships_view").as((qb) => {
  return qb
    .select({
      groupId: groupMemberships.groupId,
      partyId: groupMemberships.partyId,
      role: groupMemberRoles.role,
      groupName: sql`${groups.name}`.as('group_name'),
      groupDescription: sql`${groups.description}`.as('group_description'),
      cityName: sql`${cities.name}`.as('city_name'),
      countryName: sql`${countries.name}`.as('country_name'),
      groupCreatedAt: sql`${groups.createdAt}`.as('group_created_at'),
      membershipStart: sql`${groupMemberships.effectiveFrom}`.as('membership_start'),
      membershipEnd: sql`${groupMemberships.effectiveTo}`.as('membership_end'),
      roleStart: sql`${groupMemberRoles.effectiveFrom}`.as('role_start'),
      roleEnd: sql`${groupMemberRoles.effectiveTo}`.as('role_end'),
      isManaged: sql`${groups.isManaged}`.as('is_managed'),
      initialPurpose: sql`${groups.initialPurpose}`.as('initial_purpose'),
    })
    .from(groupMemberships)
    .innerJoin(groups, sql`${groupMemberships.groupId} = ${groups.id}`)
    .leftJoin(
      groupMemberRoles,
      sql`${groupMemberships.groupId} = ${groupMemberRoles.groupId} AND ${groupMemberships.partyId} = ${groupMemberRoles.partyId} AND ${groupMemberRoles.effectiveFrom} <= NOW() AND (${groupMemberRoles.effectiveTo} IS NULL OR ${groupMemberRoles.effectiveTo} > NOW())`
    )
    .leftJoin(cities, sql`${groups.cityId} = ${cities.id}`)
    .leftJoin(countries, sql`${groups.countryId} = ${countries.id}`)
    .where(
      sql`${groupMemberships.effectiveFrom} <= NOW() AND (${groupMemberships.effectiveTo} IS NULL OR ${groupMemberships.effectiveTo} > NOW())`
    );
});

// Materialized view for detailed group information with comprehensive data
export const groupsDetailedView = pgMaterializedView("groups_detailed_view").as((qb) => {
  return qb
    .select({
      id: groups.id,
      partyId: groups.partyId,
      name: groups.name,
      description: groups.description,
      cityId: groups.cityId,
      countryId: groups.countryId,
      initialPurpose: groups.initialPurpose,
      isManaged: groups.isManaged,
      createdAt: groups.createdAt,
      createdBy: groups.createdBy,
      creator: groups.creator,
      cityName: sql`${cities.name}`.as('city_name'),
      countryName: sql`${countries.name}`.as('country_name'),
      provinceStateName: sql`${provinceState.name}`.as('province_state_name'),
      creatorFirstName: sql`${individual.firstName}`.as('creator_first_name'),
      creatorLastName: sql`${individual.lastName}`.as('creator_last_name'),
      memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`.as('member_count'),
      vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`.as('vehicle_count'),
      adminCount: sql<number>`cast(count(distinct case when ${groupMemberRoles.role} = 'ADMIN' then ${groupMemberRoles.partyId} end) as integer)`.as('admin_count'),
    })
    .from(groups)
    .leftJoin(cities, sql`${groups.cityId} = ${cities.id}`)
    .leftJoin(countries, sql`${groups.countryId} = ${countries.id}`)
    .leftJoin(provinceState, sql`${cities.provinceStateId} = ${provinceState.id}`)
    .leftJoin(party, sql`${groups.creator} = ${party.id}`)
    .leftJoin(individual, sql`${party.id} = ${individual.partyId}`)
    .leftJoin(
      groupMemberships,
      sql`${groups.id} = ${groupMemberships.groupId} AND ${groupMemberships.effectiveFrom} <= NOW() AND (${groupMemberships.effectiveTo} IS NULL OR ${groupMemberships.effectiveTo} > NOW())`
    )
    .leftJoin(
      groupSharedVehicles,
      sql`${groups.id} = ${groupSharedVehicles.groupId} AND ${groupSharedVehicles.isActive} = true AND ${groupSharedVehicles.effectiveFrom} <= NOW() AND (${groupSharedVehicles.effectiveTo} IS NULL OR ${groupSharedVehicles.effectiveTo} > NOW())`
    )
    .leftJoin(
      groupMemberRoles,
      sql`${groups.id} = ${groupMemberRoles.groupId} AND ${groupMemberRoles.effectiveFrom} <= NOW() AND (${groupMemberRoles.effectiveTo} IS NULL OR ${groupMemberRoles.effectiveTo} > NOW())`
    )
    .groupBy(
      groups.id,
      groups.partyId,
      groups.name,
      groups.description,
      groups.cityId,
      groups.countryId,
      groups.initialPurpose,
      groups.isManaged,
      groups.createdAt,
      groups.createdBy,
      groups.creator,
      cities.name,
      countries.name,
      provinceState.name,
      individual.firstName,
      individual.lastName
    );
});

// =====================================================
// IMMUTABLE BOOKING AND POSSESSION SYSTEM - NEW ENUMS
// =====================================================

export const bookingStatusEnum = pgEnum('booking_status', [
  'DRAFT',
  'PENDING', 
  'CONFIRMED',
  'IN_PROGRESS',
  'COMPLETED',
  'CANCELLED',
  'NO_SHOW',
  'DISPUTED'
]);

export const handoverStatusEnum = pgEnum('handover_status', [
  'SCHEDULED',
  'IN_PROGRESS',
  'COMPLETED', 
  'FAILED',
  'DISPUTED'
]);

export const handoverTypeEnum = pgEnum('handover_type', [
  'BOOKING_START',
  'BOOKING_END',
  'OWNERSHIP_TRANSFER',
  'MAINTENANCE_DROP',
  'MAINTENANCE_PICKUP'
]);

export const inspectionTypeEnum = pgEnum('inspection_type', [
  'PRE_HANDOVER',
  'POST_HANDOVER',
  'PERIODIC',
  'INCIDENT', 
  'MAINTENANCE'
]);

export const inspectionStatusEnum = pgEnum('inspection_status', [
  'SCHEDULED',
  'IN_PROGRESS',
  'COMPLETED',
  'DISPUTED',
  'CANCELLED'
]);

export const possessionTypeEnum = pgEnum('possession_type', [
  'OWNER',
  'RENTER',
  'BORROWER',
  'MAINTENANCE'
]);

export const permissionTypeEnum = pgEnum('permission_type', [
  'OWNER',
  'AUTHORIZED_USER',
  'GROUP_MEMBER'
]);

export const issueTypeEnum = pgEnum('issue_type', [
  'CONDITION_DISPUTE',
  'NO_SHOW',
  'LATE_ARRIVAL',
  'MISSING_ITEMS',
  'DAMAGE_CLAIM',
  'OTHER'
]);

export const photoTypeEnhanced = pgEnum('photo_type_enhanced', [
  'left_view',
  'right_view', 
  'rear_view',
  'front_view',
  'dashboard',
  'seats_view',
  'interior',
  'odometer',
  'damage_detail',
  'tires',
  'inspector_signature'
]);

export const conditionEnhanced = pgEnum('condition_enhanced', [
  'excellent',
  'good',
  'fair',
  'poor'
]);

// =====================================================
// IMMUTABLE EVENT-BASED TABLES
// =====================================================

// Immutable booking events - never updated, only new records added
export const bookingEvents = pgTable("booking_events", {
  id: serial().primaryKey().notNull(),
  bookingReference: varchar("booking_reference", { length: 50 }).notNull(),
  vehicleId: integer("vehicle_id").notNull().references(() => vehicles.id),
  borrowerPartyId: integer("borrower_party_id").notNull().references(() => party.id),
  
  // Event details
  eventType: varchar("event_type").notNull(), // 'CREATED', 'STATUS_CHANGED', 'TIMES_UPDATED', etc.
  status: bookingStatusEnum().notNull(),
  
  // Timing (all times are recorded as they were at this event)
  requestedStart: timestamp("requested_start", { withTimezone: true, mode: 'string' }).notNull(),
  requestedEnd: timestamp("requested_end", { withTimezone: true, mode: 'string' }).notNull(),
  confirmedStart: timestamp("confirmed_start", { withTimezone: true, mode: 'string' }),
  confirmedEnd: timestamp("confirmed_end", { withTimezone: true, mode: 'string' }),
  actualStart: timestamp("actual_start", { withTimezone: true, mode: 'string' }),
  actualEnd: timestamp("actual_end", { withTimezone: true, mode: 'string' }),
  
  // Who made this change
  changedBy: integer("changed_by").notNull().references(() => party.id),
  approvedBy: integer("approved_by").references(() => party.id),
  
  // Financial (as of this event)
  quotedPrice: decimal("quoted_price", { precision: 10, scale: 2 }),
  finalPrice: decimal("final_price", { precision: 10, scale: 2 }),
  currency: varchar({ length: 3 }).default('USD'),
  
  // Additional info
  purpose: text(),
  specialRequirements: text("special_requirements"),
  notes: text(),
  changeReason: text("change_reason"), // Why this event happened
  
  // Immutable timestamp
  eventTimestamp: timestamp("event_timestamp", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("booking_events_reference_idx").on(table.bookingReference),
  index("booking_events_vehicle_id_idx").on(table.vehicleId),
  index("booking_events_borrower_party_id_idx").on(table.borrowerPartyId),
  index("booking_events_status_idx").on(table.status),
  index("booking_events_timestamp_idx").on(table.eventTimestamp),
]);

// Immutable vehicle possession records - tracks who has the vehicle when
export const vehiclePossessionEvents = pgTable("vehicle_possession_events", {
  id: serial().primaryKey().notNull(),
  vehicleId: integer("vehicle_id").notNull().references(() => vehicles.id),
  possessorPartyId: integer("possessor_party_id").notNull().references(() => party.id),
  
  // Possession period
  possessionStart: timestamp("possession_start", { withTimezone: true, mode: 'string' }).notNull(),
  possessionEnd: timestamp("possession_end", { withTimezone: true, mode: 'string' }), // NULL means current
  
  possessionType: possessionTypeEnum("possession_type").notNull(),
  
  // What triggered this possession change
  triggerType: varchar("trigger_type").notNull(), // 'BOOKING', 'MAINTENANCE', 'OWNERSHIP_TRANSFER', 'MANUAL'
  triggerReference: varchar("trigger_reference"), // booking reference, maintenance ID, etc.
  handoverId: integer("handover_id").references(() => vehicleHandovers.id),
  
  // Who recorded this event
  recordedBy: integer("recorded_by").notNull().references(() => party.id),
  notes: text(),
  
  // Immutable timestamp
  eventTimestamp: timestamp("event_timestamp", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("vehicle_possession_events_vehicle_id_idx").on(table.vehicleId),
  index("vehicle_possession_events_possessor_idx").on(table.possessorPartyId),
  index("vehicle_possession_events_dates_idx").on(table.possessionStart, table.possessionEnd),
  index("vehicle_possession_events_timestamp_idx").on(table.eventTimestamp),
]);

// Vehicle access permissions - immutable grants/revokes
export const vehicleAccessEvents = pgTable("vehicle_access_events", {
  id: serial().primaryKey().notNull(),
  vehicleId: integer("vehicle_id").notNull().references(() => vehicles.id),
  partyId: integer("party_id").notNull().references(() => party.id),
  
  eventType: varchar("event_type").notNull(), // 'GRANTED', 'REVOKED', 'UPDATED'
  permissionType: permissionTypeEnum("permission_type").notNull(),
  
  effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).notNull(),
  effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }), // NULL means indefinite
  
  grantedBy: integer("granted_by").notNull().references(() => party.id),
  reason: text(),
  
  // Immutable timestamp
  eventTimestamp: timestamp("event_timestamp", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("vehicle_access_events_vehicle_id_idx").on(table.vehicleId),
  index("vehicle_access_events_party_id_idx").on(table.partyId),
  index("vehicle_access_events_effective_idx").on(table.effectiveFrom, table.effectiveTo),
  index("vehicle_access_events_timestamp_idx").on(table.eventTimestamp),
]);

// =====================================================
// HANDOVER MANAGEMENT (IMMUTABLE)
// =====================================================

export const vehicleHandovers = pgTable("vehicle_handovers", {
  id: serial().primaryKey().notNull(),
  vehicleId: integer("vehicle_id").notNull().references(() => vehicles.id),
  handoverType: handoverTypeEnum("handover_type").notNull(),
  bookingReference: varchar("booking_reference"), // Reference instead of ID to maintain link even if booking system changes
  
  // Parties involved
  fromPartyId: integer("from_party_id").notNull().references(() => party.id),
  toPartyId: integer("to_party_id").notNull().references(() => party.id),
  
  // Timing
  scheduledTime: timestamp("scheduled_time", { withTimezone: true, mode: 'string' }).notNull(),
  
  // Location
  handoverLocation: text("handover_location"),
  handoverCoordinates: text("handover_coordinates"),
  
  // Metadata - immutable at creation
  notes: text(),
  createdBy: integer("created_by").notNull().references(() => party.id),
  createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("vehicle_handovers_vehicle_id_idx").on(table.vehicleId),
  index("vehicle_handovers_booking_reference_idx").on(table.bookingReference),
  index("vehicle_handovers_scheduled_time_idx").on(table.scheduledTime),
]);

// Handover status events - track status changes immutably
export const handoverStatusEvents = pgTable("handover_status_events", {
  id: serial().primaryKey().notNull(),
  handoverId: integer("handover_id").notNull().references(() => vehicleHandovers.id),
  status: handoverStatusEnum().notNull(),
  
  // When this status became effective
  statusTimestamp: timestamp("status_timestamp", { withTimezone: true, mode: 'string' }),
  
  // Who changed the status
  changedBy: integer("changed_by").notNull().references(() => party.id),
  notes: text(),
  
  // Immutable record timestamp
  eventTimestamp: timestamp("event_timestamp", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("handover_status_events_handover_id_idx").on(table.handoverId),
  index("handover_status_events_status_idx").on(table.status),
  index("handover_status_events_timestamp_idx").on(table.statusTimestamp),
]);

// =====================================================
// INSPECTION SYSTEM (IMMUTABLE)
// =====================================================

export const vehicleInspectionsImmutable = pgTable("vehicle_inspections_immutable", {
  id: serial().primaryKey().notNull(),
  vehicleId: integer("vehicle_id").notNull().references(() => vehicles.id),
  handoverId: integer("handover_id").references(() => vehicleHandovers.id),
  inspectorPartyId: integer("inspector_party_id").notNull().references(() => party.id),
  
  // Inspection details
  inspectionType: inspectionTypeEnum("inspection_type").notNull(),
  
  // Vehicle condition snapshot
  odometer: integer().notNull(),
  fuelLevel: integer("fuel_level"), // 0-100 percentage
  scratches: conditionlevel(),
  dents: conditionlevel(),
  tires: generalcondition(),
  lights: lightscondition(),
  cleanliness: cleanlinesslevel(),
  seats: generalcondition(),
  dashboardControls: dashboardcondition("dashboard_controls"),
  odors: odorlevel(),
  
  // Additional assessments
  overallCondition: conditionEnhanced("overall_condition").notNull(),
  knownIssues: text("known_issues"),
  newDamage: text("new_damage"),
  itemsInVehicle: text("items_in_vehicle"),
  
  // Validation - immutable once set
  inspectorSignature: text("inspector_signature"),
  inspectionCompletedAt: timestamp("inspection_completed_at", { withTimezone: true, mode: 'string' }).notNull(),
  
  // Linking inspections (self-reference)
  relatedInspectionId: integer("related_inspection_id"),
  
  // Immutable creation timestamp
  createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("vehicle_inspections_immutable_vehicle_id_idx").on(table.vehicleId),
  index("vehicle_inspections_immutable_handover_id_idx").on(table.handoverId),
  index("vehicle_inspections_immutable_inspector_idx").on(table.inspectorPartyId),
  index("vehicle_inspections_immutable_completed_at_idx").on(table.inspectionCompletedAt),
  check("fuel_level_range", sql`fuel_level >= 0 AND fuel_level <= 100`),
  foreignKey({
    columns: [table.relatedInspectionId],
    foreignColumns: [table.id],
    name: "vehicle_inspections_immutable_related_inspection_fkey"
  }),
]);

// =====================================================
// SUPPORTING IMMUTABLE TABLES
// =====================================================

export const inspectionPhotos = pgTable("inspection_photos", {
  id: serial().primaryKey().notNull(),
  inspectionId: integer("inspection_id").notNull().references(() => vehicleInspectionsImmutable.id),
  photoType: photoTypeEnhanced("photo_type").notNull(),
  fileUrl: varchar("file_url").notNull(),
  description: text(),
  capturedAt: timestamp("captured_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("inspection_photos_inspection_id_idx").on(table.inspectionId),
]);

export const handoverIssues = pgTable("handover_issues", {
  id: serial().primaryKey().notNull(),
  handoverId: integer("handover_id").notNull().references(() => vehicleHandovers.id),
  reportedBy: integer("reported_by").notNull().references(() => party.id),
  issueType: issueTypeEnum("issue_type").notNull(),
  description: text().notNull(),
  reportedAt: timestamp("reported_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("handover_issues_handover_id_idx").on(table.handoverId),
  index("handover_issues_reported_at_idx").on(table.reportedAt),
]);

// Issue resolutions - separate table to track resolution history
export const handoverIssueResolutions = pgTable("handover_issue_resolutions", {
  id: serial().primaryKey().notNull(),
  issueId: integer("issue_id").notNull().references(() => handoverIssues.id),
  resolution: text().notNull(),
  resolvedBy: integer("resolved_by").notNull().references(() => party.id),
  resolvedAt: timestamp("resolved_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index("handover_issue_resolutions_issue_id_idx").on(table.issueId),
  index("handover_issue_resolutions_resolved_at_idx").on(table.resolvedAt),
]);

// =====================================================
// CURRENT STATE VIEWS (COMPUTED FROM HISTORY)
// =====================================================

// Current booking status view
export const currentBookingStatusView = pgView("current_booking_status_view").as((qb) => {
  return qb
    .select({
      bookingReference: bookingEvents.bookingReference,
      vehicleId: bookingEvents.vehicleId,
      borrowerPartyId: bookingEvents.borrowerPartyId,
      currentStatus: bookingEvents.status,
      requestedStart: bookingEvents.requestedStart,
      requestedEnd: bookingEvents.requestedEnd,
      confirmedStart: bookingEvents.confirmedStart,
      confirmedEnd: bookingEvents.confirmedEnd,
      actualStart: bookingEvents.actualStart,
      actualEnd: bookingEvents.actualEnd,
      quotedPrice: bookingEvents.quotedPrice,
      finalPrice: bookingEvents.finalPrice,
      lastUpdated: bookingEvents.eventTimestamp,
    })
    .from(bookingEvents)
    .where(
      sql`${bookingEvents.id} IN (
        SELECT MAX(id) 
        FROM ${bookingEvents} be2 
        WHERE be2.booking_reference = ${bookingEvents.bookingReference}
      )`
    );
});

// Current vehicle possession view
export const currentVehiclePossessionView = pgView("current_vehicle_possession_view").as((qb) => {
  return qb
    .select({
      vehicleId: vehiclePossessionEvents.vehicleId,
      possessorPartyId: vehiclePossessionEvents.possessorPartyId,
      possessionType: vehiclePossessionEvents.possessionType,
      possessionStart: vehiclePossessionEvents.possessionStart,
      triggerType: vehiclePossessionEvents.triggerType,
      triggerReference: vehiclePossessionEvents.triggerReference,
    })
    .from(vehiclePossessionEvents)
    .where(
      sql`${vehiclePossessionEvents.possessionEnd} IS NULL 
          AND ${vehiclePossessionEvents.id} IN (
            SELECT MAX(id) 
            FROM ${vehiclePossessionEvents} vpe2 
            WHERE vpe2.vehicle_id = ${vehiclePossessionEvents.vehicleId}
            AND vpe2.possession_end IS NULL
          )`
    );
});

// Current vehicle access permissions view
export const currentVehicleAccessView = pgView("current_vehicle_access_view").as((qb) => {
  return qb
    .select({
      vehicleId: vehicleAccessEvents.vehicleId,
      partyId: vehicleAccessEvents.partyId,
      permissionType: vehicleAccessEvents.permissionType,
      effectiveFrom: vehicleAccessEvents.effectiveFrom,
      grantedBy: vehicleAccessEvents.grantedBy,
    })
    .from(vehicleAccessEvents)
    .where(
      sql`${vehicleAccessEvents.eventType} = 'GRANTED'
          AND ${vehicleAccessEvents.effectiveFrom} <= NOW()
          AND (${vehicleAccessEvents.effectiveTo} IS NULL OR ${vehicleAccessEvents.effectiveTo} > NOW())
          AND NOT EXISTS (
            SELECT 1 FROM ${vehicleAccessEvents} vae2
            WHERE vae2.vehicle_id = ${vehicleAccessEvents.vehicleId}
            AND vae2.party_id = ${vehicleAccessEvents.partyId}
            AND vae2.event_type = 'REVOKED'
            AND vae2.event_timestamp > ${vehicleAccessEvents.eventTimestamp}
          )`
    );
});

// Current handover status view
export const currentHandoverStatusView = pgView("current_handover_status_view").as((qb) => {
  return qb
    .select({
      handoverId: handoverStatusEvents.handoverId,
      currentStatus: handoverStatusEvents.status,
      statusTimestamp: handoverStatusEvents.statusTimestamp,
      changedBy: handoverStatusEvents.changedBy,
    })
    .from(handoverStatusEvents)
    .where(
      sql`${handoverStatusEvents.id} IN (
        SELECT MAX(id)
        FROM ${handoverStatusEvents} hse2
        WHERE hse2.handover_id = ${handoverStatusEvents.handoverId}
      )`
    );
});

// Listing history table - captures all changes to listings for audit trail
export const listingHistory = pgTable("listing_history", {
	id: serial().primaryKey().notNull(),
	listingId: integer("listing_id").notNull(),
	
	// Snapshot of all listing fields at time of change
	partyId: integer("party_id").notNull(),
	vehicleId: integer("vehicle_id").notNull(),
	effectiveFrom: timestamp("effective_from", { withTimezone: true, mode: 'string' }).notNull(),
	effectiveTo: timestamp("effective_to", { withTimezone: true, mode: 'string' }),
	fraction: doublePrecision("fraction").notNull(),
	askingPrice: doublePrecision("asking_price").notNull(),
	condition: conditionenum().notNull(),
	mileage: doublePrecision(),
	listingType: listingtypeenum("listing_type").notNull(),
	audience: audienceenum().notNull(),
	
	// Change metadata
	changeType: varchar("change_type").notNull(), // 'CREATED', 'UPDATED', 'ENDED'
	changedBy: integer("changed_by").notNull(),
	changeReason: text("change_reason"),
	previousValues: jsonb("previous_values"), // What changed (for UPDATED events)
	
	// When this change was recorded
	changedAt: timestamp("changed_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("listing_history_listing_id_idx").on(table.listingId),
	index("listing_history_changed_at_idx").on(table.changedAt),
	foreignKey({
		columns: [table.listingId],
		foreignColumns: [listings.id],
		name: "listing_history_listing_id_fkey"
	}),
	foreignKey({
		columns: [table.changedBy],
		foreignColumns: [party.id],
		name: "listing_history_changed_by_fkey"
	}),
]);


// Push subscriptions table for PWA notifications
export const pushSubscriptions = pgTable("push_subscriptions", {
	id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
	userId: text("user_id").notNull(), // AWS Cognito user ID
	partyId: integer("party_id"), // Reference to your party system
	endpoint: text("endpoint").notNull(),
	p256dhKey: text("p256dh_key").notNull(),
	authKey: text("auth_key").notNull(),
	expirationTime: timestamp("expiration_time"),
	userAgent: text("user_agent"),
	deviceInfo: json("device_info"), // Store device details
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
  });
  
  // Notification log for tracking
  export const notificationLogs = pgTable("notification_logs", {
	id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
	userId: text("user_id").notNull(),
	subscriptionId: text("subscription_id").references(() => pushSubscriptions.id),
	title: text("title").notNull(),
	body: text("body").notNull(),
	payload: json("payload").notNull(),
	sentAt: timestamp("sent_at").defaultNow().notNull(),
	deliveryStatus: text("delivery_status", { 
	  enum: ["pending", "sent", "failed", "expired"] 
	}).default("pending"),
	errorMessage: text("error_message"),
	clickedAt: timestamp("clicked_at"),
  });
  
  export type PushSubscription = typeof pushSubscriptions.$inferSelect;
  export type InsertPushSubscription = typeof pushSubscriptions.$inferInsert;
      export type NotificationLog = typeof notificationLogs.$inferSelect;
    export type InsertNotificationLog = typeof notificationLogs.$inferInsert;
    
    export type Task = typeof tasks.$inferSelect;
    export type InsertTask = typeof tasks.$inferInsert;
