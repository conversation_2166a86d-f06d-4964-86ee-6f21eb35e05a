import {
  pgTable,
  varchar,
  text,
  timestamp,
  numeric,
  integer,
  serial,
} from "drizzle-orm/pg-core";
import { groups, users } from "../schema";
import { party } from "../schema";
import { temporalFields } from "./temporals";
import { decimal } from "drizzle-orm/pg-core";
import { applicationStatusEnum, documentStatusEnum } from "./enums";
import { h_listings } from "./listings";

// Applications Table - key admin fields as columns, NO NULLS!
export const h_applications = pgTable("h_applications", {
  id: serial().primaryKey().notNull(),
  applicantId: integer("applicant_id")
    .references(() => party.id)
    .notNull(),
  listingId: integer("listing_id")
    .references(() => h_listings.id)
    .notNull(), // Always required - no nulls!

  // EVERYTHING ELSE - JSON for flexibility
  applicationDetails: text("application_details"), // JSON for type-specific fields
  /* Examples:
   * Catalog/E-hailing: {"arrangementRequested": true, experience: "hasEhailingExperience: true", "drivingExperienceYears": 5", ehailingCompany: "Uber", "ehailingProfileNumber": "78432", "ehailingWorkType": "full-time"}
   * Vehicle/Rental: {"purpose": "business", applicantPreferences: {"minAge": 25, "drivingExperienceYears": 5, "gender": "female"}}
   * Vehicle/Fractional: {"applicantPreferences: {"minAge": 25, "drivingExperienceYears": 5, "gender": "female"}}
   */
  // ...temporalFields,
});

// Application Documents - supporting documents for applications
export const h_applicationDocuments = pgTable("h_application_documents", {
  id: serial().primaryKey().notNull(),
  applicationId: integer("application_id")
    .references(() => h_applications.id, { onDelete: "cascade" })
    .notNull(),
  documentType: varchar().notNull(),
  documentUrl: varchar().notNull(),
  uploadedAt: timestamp("uploaded_at", { withTimezone: true })
    .defaultNow()
    .notNull(),

  // ...temporalFields,
});

// Application Documents status
export const h_applicationDocumentsStatus = pgTable(
  "h_application_documents_status",
  {
    id: serial().primaryKey().notNull(),
    applicationDocumentId: integer("application_document_id")
      .notNull()
      .references(() => h_applicationDocuments.id),
    status: documentStatusEnum("status").notNull(),
    statusAt: timestamp("status_at", { withTimezone: true }).notNull(),
    statusBy: integer("status_by").references(() => party.id),
  }
);

// Application Decisions - append-only decision log
export const h_applicationDecisions = pgTable("h_application_decisions", {
  id: serial().primaryKey().notNull(),
  decision: applicationStatusEnum("decision").notNull(),
  reason: text("reason"),
  applicationId: integer("application_id")
    .notNull()
    .references(() => h_applications.id, { onDelete: "cascade" }),
  reviewerId: integer("reviewer_id")
    .notNull()
    .references(() => users.id),
  decisionAt: timestamp("decision_at", { withTimezone: true }).notNull(),
  // ...temporalFields,
});

// Application History - append-only status change log
// export const applicationHistory = pgTable("application_history", {
//   id: text("id")
//     .primaryKey()
//     .$defaultFn(() => crypto.randomUUID()),
//   previousStatus: applicationStatusEnum("previous_status"),
//   newStatus: applicationStatusEnum("new_status").notNull(),
//   note: text("note"),
//   applicationId: text("application_id")
//     .notNull()
//     .references(() => applications.id, { onDelete: "cascade" }),
//   userId: text("user_id").references(() => users.id),
//   ...temporalFields,
// });
